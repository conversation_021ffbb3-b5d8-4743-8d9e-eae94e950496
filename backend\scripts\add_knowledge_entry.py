from backend.database import SessionLocal
from backend.models import KnowledgeBase, User, Project
from datetime import datetime

def add_knowledge_entry():
    """Add a sample knowledge base entry to the database"""
    db = SessionLocal()
    try:
        # Check if we have any users
        user = db.query(User).first()
        if not user:
            print("No users found in the database. Please create a user first.")
            return
        
        # Check if we have any projects
        project = db.query(Project).first()
        if not project:
            print("No projects found in the database. Please create a project first.")
            return
        
        # Create a sample knowledge base entry
        entry = KnowledgeBase(
            project_id=project.project_id,
            title="Sample Knowledge Entry",
            content="This is a sample knowledge base entry created for testing purposes.",
            uploaded_by=user.user_id,
            created_at=datetime.now()
        )
        
        db.add(entry)
        db.commit()
        
        print(f"Successfully added knowledge base entry with ID: {entry.kb_id}")
    except Exception as e:
        db.rollback()
        print(f"Error adding knowledge base entry: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    add_knowledge_entry()