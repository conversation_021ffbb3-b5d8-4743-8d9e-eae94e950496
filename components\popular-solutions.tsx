import { Card, CardContent } from "@/components/ui/card"
import { CheckCircle2 } from "lucide-react"

// Mock data for popular solutions
const popularSolutions = [
  {
    id: 1,
    title: "Resolving database connection timeouts in OMS",
    system: "OMS",
    type: "Database",
    verified: true,
    views: 342,
    createdAt: "2 days ago",
  },
  {
    id: 2,
    title: "Fixing API authentication failures in inventory sync",
    system: "WMS",
    type: "API",
    verified: true,
    views: 256,
    createdAt: "1 week ago",
  },
  {
    id: 3,
    title: "Troubleshooting UI rendering errors in product catalog",
    system: "OMS",
    type: "UI",
    verified: false,
    views: 189,
    createdAt: "3 days ago",
  },
  {
    id: 4,
    title: "Resolving stuck automation workflows",
    system: "Automation",
    type: "Workflow",
    verified: true,
    views: 421,
    createdAt: "5 days ago",
  },
]

export function PopularSolutions() {
  return (
    <div className="space-y-3">
      {popularSolutions.map((solution) => (
        <Card key={solution.id} className="overflow-hidden">
          <CardContent className="p-3">
            <div className="flex flex-col gap-2">
              <div className="flex items-start justify-between">
                <h3 className="font-medium flex items-center gap-1">
                  {solution.title}
                  {solution.verified && <CheckCircle2 className="h-4 w-4 text-green-500" />}
                </h3>
              </div>
              <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                <span className="inline-flex items-center rounded-full border px-2 py-0.5">{solution.system}</span>
                <span className="inline-flex items-center rounded-full border px-2 py-0.5">{solution.type}</span>
                <span className="inline-flex items-center rounded-full border px-2 py-0.5">{solution.views} views</span>
                <span className="ml-auto">{solution.createdAt}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

