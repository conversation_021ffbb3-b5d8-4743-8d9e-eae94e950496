import { useState, useEffect } from 'react';
import { useDebounce } from './use-debounce';
import { useSession } from 'next-auth/react';

interface SearchResult {
  id: number;
  title: string;
  description?: string;
  error_message?: string;
  steps?: string;
  type?: "issue" | "knowledge";
}

interface UseSearchProps {
  projectId?: number | null;
  type?: "issues" | "knowledge" | "all";
  initialQuery?: string;
}

export function useSearch({ projectId, type = "all", initialQuery = "" }: UseSearchProps) {
  const [query, setQuery] = useState(initialQuery);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const debouncedQuery = useDebounce(query, 300);
  const { data: session } = useSession();

  // Common function to perform the search
  const performSearch = async (searchQuery: string): Promise<SearchResult[]> => {
    if (!searchQuery.trim()) {
      console.log('No search query, clearing results');
      setResults([]);
      return [];
    }

    console.log('Starting search for:', searchQuery);
    setIsLoading(true);
    setError(null);

    try {
      // Build the URL with or without project_id based on whether it's provided
      let searchUrl = `/api/search?query=${encodeURIComponent(searchQuery)}&type=${type}`;
      
      // Only add project_id parameter if it's a valid project ID (greater than 0)
      if (projectId && projectId > 0) {
        searchUrl += `&project_id=${projectId}`;
      }
      
      console.log('Search URL:', searchUrl);
      
      // Get the token from the session
      const token = session?.user?.accessToken;
      
      if (!token) {
        const errorMsg = 'Authentication required. Please log in again.';
        console.error('No authentication token found in session');
        setError(errorMsg);
        setResults([]);
        throw new Error(errorMsg);
      }

      const response = await fetch(searchUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        cache: 'no-store' // Ensure fresh results
      });
      
      console.log('Search response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        const errorMsg = `Search failed: ${response.status} ${response.statusText}`;
        console.error('Search failed with status:', response.status, 'Response:', errorText);
        setError(errorMsg);
        setResults([]);
        throw new Error(errorMsg);
      }

      const data = await response.json();
      console.log('Raw search results:', data);
      
      // Handle both array and { items: [...] } response formats
      const results = Array.isArray(data) ? data : (data.items || []);
      console.log('Processed search results:', results);
      
      setResults(results);
      return results;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to perform search';
      console.error('Search error:', err);
      setError(errorMsg);
      setResults([]);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Effect for debounced search
  useEffect(() => {
    const searchData = async () => {
      try {
        await performSearch(debouncedQuery);
      } catch (err) {
        // Error is already handled in performSearch
      }
    };

    searchData();
  }, [debouncedQuery, projectId, type]);
  
  // Function to manually trigger a search
  const manualSearch = async (searchQuery: string): Promise<SearchResult[]> => {
    setQuery(searchQuery);
    return performSearch(searchQuery);
  };

  return {
    query,
    setQuery,
    results,
    isLoading,
    error,
    performSearch,
    manualSearch,
  };
}
