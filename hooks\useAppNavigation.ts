import { useMemo } from 'react';
import { useUser } from '@/contexts/user-context';
import { Home, FileText, Database, Shield } from 'lucide-react';

export function useAppNavigation(projectId: string | null) {
  const { is_admin } = useUser();
  
  return useMemo(() => {
    const baseNav = [
      { name: "Dashboard", href: `/dashboard?project=${projectId}`, icon: Home },
      { name: "Issues", href: `/issues?project=${projectId}`, icon: FileText },
      { name: "Knowledge Bank", href: `/knowledge?project=${projectId}`, icon: Database }
    ];

    if (is_admin) {
      return [
        ...baseNav,
        { name: "Admin", href: `/admin?project=${projectId}`, icon: Shield }
      ];
    }

    return baseNav;
  }, [projectId, is_admin]);
}
