import sys
import os
from pathlib import Path

# Get the absolute path to the project root directory (one level up from backend)
project_root = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(project_root))

from backend.database import Base, SessionLocal
from backend.models import User
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def update_password_hashes():
    db = SessionLocal()
    try:
        users = db.query(User).all()
        for user in users:
            current_password = str(user.password_hash)  # Convert Column to string
            
            if not current_password:  # Skip users with no password
                print(f"Skipping user {user.user_id} (no password set)")
                continue

            if pwd_context.identify(current_password) is None:  # Check if not already hashed
                hashed_password = pwd_context.hash(current_password)  # Hash the plaintext password
                # Update using SQLAlchemy's proper method
                db.query(User).filter(User.user_id == user.user_id).update(
                    {"password_hash": hashed_password},
                    synchronize_session=False
                )
                print(f"Updated password for user {user.user_id}")

        db.commit()
        print("✅ Successfully updated password hashes")
    
    except Exception as e:
        print(f"❌ Error updating passwords: {e}")
        db.rollback()
    
    finally:
        db.close()

if __name__ == "__main__":
    update_password_hashes()
