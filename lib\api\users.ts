import { api } from './client';
import { User } from './types';

// Use relative path since API client will prepend the base URL
const USERS_BASE = '/users';

export const usersApi = {
  /**
   * Get current user profile
   */
  getCurrentUser: () => api.get<User>('/me'),

  /**
   * Get user by ID
   */
  getUser: (userId: number) => api.get<User>(`${USERS_BASE}/${userId}`),

  /**
   * Update user profile
   */
  updateUser: (userId: number, userData: Partial<User>) =>
    api.put<User>(`${USERS_BASE}/${userId}`, userData),

  /**
   * Get all users (admin only)
   * @returns Promise with an array of User objects
   */
  getAllUsers: () => {
    return api.get<User[]>(USERS_BASE);
  },

  /**
   * Delete a user (admin only)
   */
  deleteUser: (userId: number) => api.delete<{ success: boolean }>(`${USERS_BASE}/${userId}`),

  /**
   * Update user role (admin only)
   */
  updateUserRole: (userId: number, role: string) =>
    api.patch<User>(`${USERS_BASE}/${userId}/role`, { role }),
};
