'use client'

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@/components/ui/tabs"
import { UserManagement } from "@/components/admin/user-management"

import { ProjectManagement } from "@/components/admin/project-management"
import { useRouter, useSearchParams } from "next/navigation"
import { useCallback, Suspense } from "react"
import { Loader } from "@/components/ui/loader"

const ADMIN_TABS = [
  {
    value: "users", 
    title: "User Management",
    description: "View and manage user accounts",
    component: <UserManagement />,
  },
  {
    value: "projects",
    title: "Project Management",
    description: "Manage projects and user access",
    component: <ProjectManagement />,
  },
] as const

// Create a TypeScript union type from tab values
type TabValue = typeof ADMIN_TABS[number]["value"]

// AdminContent wrapper component
export default function AdminPage() {
  return (
    <Suspense fallback={<AdminLoading />}>
      <AdminContent />
    </Suspense>
  );
}

// Loading state component
function AdminLoading() {
  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col gap-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Admin Dashboard
          </h1>
          <p className="text-muted-foreground">
            <div className="flex justify-center items-center h-full"><Loader /></div>
          </p>
        </div>
        <div className="h-[400px] flex items-center justify-center">
          <div className="flex justify-center items-center h-full"><Loader /></div>
        </div>
      </div>
    </div>
  );
}

// Main admin content component
function AdminContent() {
  const searchParams = useSearchParams()
  const router = useRouter()

  // Extract tab param safely
  const tabParam = searchParams?.get("tab")

  // Validate the tab value to ensure it exists in ADMIN_TABS
  const isValidTab = ADMIN_TABS.some(tab => tab.value === tabParam)
  const currentTab: TabValue = isValidTab ? (tabParam as TabValue) : ADMIN_TABS[0].value

  const handleTabChange = useCallback(
    (newValue: string) => {
      const params = new URLSearchParams(searchParams?.toString())
      params.set("tab", newValue)
      router.replace(`?${params.toString()}`, { scroll: false })
    },
    [router, searchParams]
  )

  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col gap-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Admin Dashboard
          </h1>
          <p className="text-muted-foreground">
            Manage users, validate content, and view system analytics
          </p>
        </div>

        <Tabs defaultValue={currentTab} onValueChange={handleTabChange}>
          <TabsList className="mb-4">
            {ADMIN_TABS.map((tab) => (
              <TabsTrigger key={tab.value} value={tab.value}>
                {tab.title}
              </TabsTrigger>
            ))}
          </TabsList>

          {ADMIN_TABS.map((tab) => (
            <TabsContent key={tab.value} value={tab.value}>
              <Card>
                <CardHeader>
                  <CardTitle>{tab.title}</CardTitle>
                  <CardDescription>{tab.description}</CardDescription>
                </CardHeader>
                <CardContent>{tab.component}</CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  )
}
