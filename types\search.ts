/**
 * Type representing the possible types of search results
 */
export type SearchResultType = 'issue' | 'knowledge';

export type SearchAPIResultType = 'issues' | 'knowledge' | 'all';

/**
 * Interface representing a search result item from the API
 */
interface BaseSearchResult {
  id: number;
  title: string;
  description?: string | undefined;
  error_message?: string | undefined;
  steps?: string | undefined;
  type?: SearchResultType | undefined;
}

/**
 * Interface representing a search result item in the UI
 */
export interface SearchResult extends Omit<BaseSearchResult, 'type'> {
  type: SearchResultType;
  description?: string | undefined;
  error_message?: string | undefined;
  steps?: string | undefined;
}

/**
 * Interface for issue details
 */
export interface IssueDetails {
  id: number;
  title: string;
  errorMessage: string;
  errorCode: string;
  system_name: string;
  type: string;
  status: string;
  impact: string;
  frequency: string;
  category: string;
  description: string;
  createdBy: string;
  createdAt: string;
  comments: number;
  solution: {
    exists: boolean;
    steps?: string[];
    description?: string;
  };
}

/**
 * Props for the SearchResults component
 */
export interface SearchResultsProps {
  results: SearchResult[];
  onResultClick: (result: SearchResult) => void;
  isLoading?: boolean;
}
