"use client"

import { Suspense, useState, useEffect } from "react"
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { PlusCircle } from "lucide-react"
import { IssueFormDialog } from "@/components/issue-form-dialog"
import { IssuesList } from "@/components/issues-list-component"
import { useSession } from "next-auth/react"
import { useToast } from "@/components/ui/use-toast"
import { issueApi, solutionApi, IssueCreateData, SolutionCreateData, CategoryEnum, StatusEnum, ImpactEnum, FrequencyEnum, SystemNameEnum } from "@/lib/api"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

function IssuesContent() {
  const { data: session, status } = useSession()
  const { toast } = useToast()
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isFetchingSolutions, setIsFetchingSolutions] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const MAX_RETRIES = 2
  // Define a proper type for the issues state
  interface MappedIssue {
    id: number;
    title: string;
    error_code?: string;
    error_message?: string;
    system_name: SystemNameEnum;
    type?: string;
    status: StatusEnum;
    impact?: string;
    frequency?: string;
    category?: CategoryEnum;
    description: string;
    createdBy: string;
    createdAt: string;
    jira_id?: string | null;
    jira_link?: string | null;
    hemants_view?: string | null;
    solution: {
      exists: boolean;
      content?: string;
      verified?: boolean;
      author?: string;
      createdAt?: string;
    };
  }

  const [issues, setIssues] = useState<MappedIssue[]>([])

  // Get project ID from URL parameters or session storage
  const searchParams = useSearchParams();
  const router = useRouter();
  const [projectId, setProjectId] = useState<number | null>(null);
  
  // Handle initial project ID setup and data fetching
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    let mounted = true;
    let resolvedProjectId: number | null = null;

    // Try to get project ID from URL first
    const projectParam = searchParams.get("project");
    if (projectParam) {
      const id = parseInt(projectParam, 10);
      if (!isNaN(id) && id > 0) {
        resolvedProjectId = id;
      }
    }

    // If no valid project in URL, try session storage
    if (!resolvedProjectId) {
      const storedProjectId = sessionStorage.getItem('currentProjectId');
      if (storedProjectId) {
        const id = parseInt(storedProjectId, 10);
        if (!isNaN(id) && id > 0) {
          resolvedProjectId = id;
          // Update URL to reflect the stored project
          if (mounted) {
            router.replace(`/issues?project=${id}`);
          }
        }
      }
    }

    // If we have a valid project ID, set it and fetch issues
    if (resolvedProjectId) {
      if (mounted) {
        setProjectId(resolvedProjectId);
        setError(null);
        sessionStorage.setItem('currentProjectId', resolvedProjectId.toString());
      }
    } else if (mounted) {
      // If we get here, the project ID is invalid or missing
      router.push("/dashboard");
    }

    return () => {
      mounted = false;
    };
  }, [searchParams, router]);

  // Fetch issues when projectId changes
  useEffect(() => {
    if (projectId && projectId > 0) {
      const fetchData = async () => {
        try {
          setIsLoading(true);
          await fetchIssues();
        } catch (err) {
          console.error('Error fetching issues:', err);
          if (err instanceof Error) {
            setError(err.message);
          } else {
            setError('An unknown error occurred');
          }
        } finally {
          setIsLoading(false);
        }
      };

      fetchData();
    }
  }, [projectId, retryCount]);

  const handleRetry = () => {
    if (retryCount < MAX_RETRIES) {
      setRetryCount(prev => prev + 1);
      setError(null);
    } else {
      // After max retries, redirect to login
      router.push('/auth/signin');
    }
  };

  const fetchIssues = async (): Promise<void> => {
    // Don't proceed if we don't have a valid project ID
    if (!projectId || projectId <= 0) {
      console.log('Invalid or missing project ID');
      return;
    }

    // Check if session is still loading
    if (status === 'loading') {
      console.log('Session is still loading...');
      return;
    }

    // Check if user is authenticated
    if (!session) {
      console.log('No active session found, redirecting to login...');
      router.push('/auth/signin');
      return;
    }

    try {
      // Check for access token
      const accessToken = session.user?.accessToken;
      if (!accessToken) {
        console.error('Authentication token not found in session:', session);
        if (retryCount < MAX_RETRIES) {
          // Try to refresh the session
          const newSession = await fetch('/api/auth/session');
          const sessionData = await newSession.json();
          
          if (sessionData?.user?.accessToken || sessionData?.accessToken) {
            // If we got a new token, update the session and retry
            return handleRetry();
          }
        }
        throw new Error("Your session has expired. Please sign in again.");
      }

      // Ensure projectId is a valid number
      const currentProjectId = Number(projectId);
      if (isNaN(currentProjectId) || currentProjectId <= 0) {
        throw new Error("Invalid project ID");
      }

      // Use the correct API function that matches the backend endpoint
      const response = await issueApi.searchIssues(currentProjectId);

      // Log the raw response to see what categories are coming from the API
      console.log("API Response Items:", response.items.map((item: any) => ({
        id: item.issue_id,
        category: item.category,
        title: item.title
      })));

      // Log the CategoryEnum values for comparison
      console.log("CategoryEnum values:", {
        Exceptions: CategoryEnum.Exceptions,
        CommonIssues: CategoryEnum.CommonIssues,
        Misc: CategoryEnum.Misc
      });

      // First map the basic issue data
      const mappedIssues: MappedIssue[] = response.items.map((issue: any) => {
        // Add more detailed logging for category debugging
        console.log(`Processing issue ${issue.issue_id} with category:`, {
          rawCategory: issue.category,
          categoryType: typeof issue.category,
          categoryEnum: CategoryEnum,
          matchesExceptions: issue.category === CategoryEnum.Exceptions,
          matchesCommonIssues: issue.category === CategoryEnum.CommonIssues,
          matchesMisc: issue.category === CategoryEnum.Misc,
          upperCaseMatches: {
            exceptions: String(issue.category).toUpperCase() === CategoryEnum.Exceptions.toUpperCase(),
            commonIssues: String(issue.category).toUpperCase() === CategoryEnum.CommonIssues.toUpperCase(),
            misc: String(issue.category).toUpperCase() === CategoryEnum.Misc.toUpperCase()
          }
        });
        // Log each issue's category for debugging
        console.log(`Issue ${issue.issue_id} category:`, {
          rawCategory: issue.category,
          categoryType: typeof issue.category,
          matchesExceptions: issue.category === CategoryEnum.Exceptions,
          matchesCommonIssues: issue.category === CategoryEnum.CommonIssues,
          matchesMisc: issue.category === CategoryEnum.Misc
        });

        // Normalize the category to ensure it matches one of our enum values
        let category = issue.category;

        // Log the raw category for debugging
        console.log(`Raw category for issue ${issue.issue_id}:`, {
          category,
          type: typeof category,
          stringValue: String(category),
          charCodes: Array.from(String(category)).map(c => c.charCodeAt(0))
        });

        // Use exact string values that match the database
        if (String(category).includes('COMMON') || String(category).includes('Common')) {
          category = CategoryEnum.CommonIssues;
          console.log(`Issue ${issue.issue_id}: Normalized to ${CategoryEnum.CommonIssues}`);
        } else if (String(category).includes('IGNORED') || String(category).includes('Ignored')) {
          category = CategoryEnum.IgnoredExceptions;
          console.log(`Issue ${issue.issue_id}: Normalized to ${CategoryEnum.IgnoredExceptions}`);
        } else if (String(category).includes('EXCEPTION') || String(category).includes('Exception')) {

          category = CategoryEnum.Exceptions;
          console.log(`Issue ${issue.issue_id}: Normalized to ${CategoryEnum.Exceptions}`);
        } else if (String(category).includes('MISC') || String(category).includes('Misc')) {
          category = CategoryEnum.Misc;
          console.log(`Issue ${issue.issue_id}: Normalized to ${CategoryEnum.Misc}`);
        } else {
          console.log(`Issue ${issue.issue_id}: Could not normalize category: ${category}`);
          // Defaulting to CommonIssues if not a valid category
          category = CategoryEnum.CommonIssues;
          console.log(`Issue ${issue.issue_id}: Defaulting to ${CategoryEnum.CommonIssues}`);
        }

        // Final validation to ensure category is a valid enum value
        if (category !== CategoryEnum.CommonIssues &&
            category !== CategoryEnum.Exceptions &&
            category !== CategoryEnum.IgnoredExceptions &&
            category !== CategoryEnum.Misc) {
          category = CategoryEnum.CommonIssues;
          console.log(`Issue ${issue.issue_id}: Forced to ${CategoryEnum.CommonIssues} after validation`);
        }

        const mappedIssue: MappedIssue = {
          id: issue.issue_id,
          title: issue.title || "",
          error_code: issue.error_code || "",
          error_message: issue.error_message || "",
          system_name: issue.system_name || SystemNameEnum.OTHERS,
          type: issue.type || "", // Fixed field name from 'type' to 'issue_type'
          status: issue.status as StatusEnum || StatusEnum.Open,
          impact: issue.impact || "",
          frequency: issue.frequency || "",
          category: category,
          description: issue.description || "",
          createdBy: issue.created_by ? String(issue.created_by) : "Unknown",
          createdAt: issue.created_at || new Date().toISOString(),
          jira_id: issue.jira_id || null,
          jira_link: issue.jira_link || null,
          hemants_view: issue.hemants_view || null,
          solution: {
            exists: false,
            content: "",
            verified: false,
            author: "",
            createdAt: ""
          }
        };

        return mappedIssue;
      }
    );

      // Set issues with basic data first so UI can render
      setIssues(mappedIssues);
      setIsLoading(false);

      // Then fetch solutions in the background
      setIsFetchingSolutions(true);

      try {
        // Fetch solutions for each issue in parallel
        const issuesWithSolutions = await Promise.all(
          mappedIssues.map(async (mappedIssue) => {
            try {
              console.log(`[DEBUG] Fetching solutions for issue ${mappedIssue.id} (${mappedIssue.title})`);
              const solutionsResponse = await solutionApi.getIssueSolutions(mappedIssue.id);
              console.log(`[DEBUG] Solutions response for issue ${mappedIssue.id}:`, {
                issueId: mappedIssue.id,
                hasSolutions: solutionsResponse?.items?.length > 0,
                solutionCount: solutionsResponse?.items?.length || 0,
                firstSolution: solutionsResponse?.items?.[0] || 'none'
              });

              // Initialize with default solution values
              const defaultSolution = {
                exists: false,
                content: "",
                verified: false,
                author: "",
                createdAt: ""
              };

              // If we have solutions, update the solution property
              if (solutionsResponse?.items?.length > 0) {
                const solution = solutionsResponse.items[0];
                // Make sure solution is defined before accessing its properties
                if (solution?.solution_text) {
                  console.log(`Found solution for issue ${mappedIssue.id}:`, solution);
                  return {
                    ...mappedIssue,
                    solution: {
                      exists: true,
                      content: solution.solution_text || "",
                      verified: false, // Default to false since verified is not part of the Solution type
                      author: solution.provided_by_name || solution.provided_by?.toString() || "",
                      createdAt: solution.created_at || ""
                    }
                  };
                }
              } else {
                console.log(`No solutions found for issue ${mappedIssue.id}`);
              }

              // Return the issue with default solution values if no solution was found
              return {
                ...mappedIssue,
                solution: defaultSolution
              };
            } catch (solutionError) {
              console.error(`Could not fetch solutions for issue ${mappedIssue.id}:`, solutionError);
              // Return the issue with default solution values on error
              return {
                ...mappedIssue,
                solution: {
                  exists: false,
                  content: "",
                  verified: false,
                  author: "",
                  createdAt: ""
                }
              };
            }
          })
        );

        // Log the final state of issues with solutions
        console.log('[DEBUG] All issues with solutions:', {
          totalIssues: issuesWithSolutions.length,
          issuesWithSolutions: issuesWithSolutions.filter(issue => issue.solution.exists).length,
          sampleIssue: issuesWithSolutions[0] ? {
            id: issuesWithSolutions[0].id,
            title: issuesWithSolutions[0].title,
            solution: issuesWithSolutions[0].solution
          } : 'no issues'
        });
        
        setIssues(issuesWithSolutions);
      } catch (error) {
        console.error("Error fetching solutions:", error);
        // If there's an error, keep the issues but log it
        toast({
          title: "Warning",
          description: "Some solutions could not be loaded. Please try refreshing the page.",
          variant: "destructive"
        });
      } finally {
        setIsFetchingSolutions(false);
      }
    } catch (error) {
      console.error("Error fetching issues:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
      setIsLoading(false);
    }
  }

  useEffect(() => {
    if (status === "authenticated" && session?.user?.accessToken) {
      fetchIssues()
    } else if (status === "unauthenticated") {
      setError("Please log in to view issues")
      setIsLoading(false)
    }
  }, [status, session, projectId])
  
  // Define the interfaces to match what IssueFormDialog expects
  interface IssueBaseData {
    project_id: number;
    category: CategoryEnum;
    title: string;
    error_code: string | null;
    error_message: string | null;
    system_name: SystemNameEnum | null;
    issue_type: string | null;
    status: StatusEnum;
    impact: ImpactEnum | null;
    frequency: FrequencyEnum | null;
    description: string;
    jira_id: string | null;
    jira_link: string | null;
    hemants_view: string | null;
  }

  interface IssueData extends IssueBaseData {
    issue_id?: number; // Present in edit mode
    solution?: SolutionData | null;
  }

  interface SolutionData {
    issue_id: number;
    category: CategoryEnum; // Changed type to CategoryEnum for consistency
    solution_text: string;
    provided_by: number;
  }

// ... (rest of the code remains the same)
  const handleSubmitIssue = async (data: { issue: IssueData; solution?: SolutionData | null }) => {
    try {
      if (!session?.user?.accessToken) {
        toast({
          title: "Authentication Error",
          description: "Please log in to submit issues",
          variant: "destructive"
        })
        return
      }

      console.log("Submitting issue:", data.issue);

      // Validate required fields
      if (!data.issue.title || !data.issue.description || !data.issue.category) {
        throw new Error("Missing required fields: title, description, or category");
      }

      // Log the category value before creating the issue
      console.log("Category before creating issue:", {
        category: data.issue.category,
        categoryType: typeof data.issue.category,
        categoryEnum: CategoryEnum,
        matchesExceptions: data.issue.category === CategoryEnum.Exceptions,
        matchesCommonIssues: data.issue.category === CategoryEnum.CommonIssues,
        matchesMisc: data.issue.category === CategoryEnum.Misc
      });

      // Log detailed information about the category
      console.log("Category before normalization:", {
        value: data.issue.category,
        type: typeof data.issue.category,
        stringValue: String(data.issue.category),
        charCodes: Array.from(String(data.issue.category)).map(c => c.charCodeAt(0)),
        enumCommonIssues: CategoryEnum.CommonIssues,
        enumCommonIssuesType: typeof CategoryEnum.CommonIssues,
        enumCommonIssuesCharCodes: Array.from(String(CategoryEnum.CommonIssues)).map(c => c.charCodeAt(0)),
        directMatch: data.issue.category === CategoryEnum.CommonIssues,
        uppercaseMatch: String(data.issue.category).toUpperCase() === String(CategoryEnum.CommonIssues).toUpperCase()
      });

      // Ensure the category is one of the valid enum values
      let category = data.issue.category;

      // Force the category to be the exact enum value expected by the database
      if (typeof category === 'string') {
        const upperCategory = category.toUpperCase();
        if (upperCategory.includes('COMMON')) {
          category = CategoryEnum.CommonIssues;
        } else if (upperCategory.includes('IGNORED')) {
          category = CategoryEnum.IgnoredExceptions;
        } else if (upperCategory.includes('EXCEPTION')) {
          category = CategoryEnum.Exceptions;
        } else if (upperCategory.includes('MISC')) {
          category = CategoryEnum.Misc;
        }
        console.log("Normalized category to:", category);
      }

      // Ensure category is always a valid CategoryEnum value
      if (category !== CategoryEnum.CommonIssues &&
          category !== CategoryEnum.Exceptions &&
          category !== CategoryEnum.Misc &&
          category !== CategoryEnum.IgnoredExceptions) {
        // Default to CommonIssues if not a valid category
        console.log("Category not valid, defaulting to CommonIssues");
        category = CategoryEnum.CommonIssues;
      }

      console.log("Normalized category:", {
        original: data.issue.category,
        normalized: category,
        directMatchAfter: category === CategoryEnum.CommonIssues,
        uppercaseMatchAfter: String(category).toUpperCase() === String(CategoryEnum.CommonIssues).toUpperCase()
      });

      // Ensure enum values are in UPPERCASE to match backend expectations
      const normalizedCategory = category.toUpperCase() as CategoryEnum;
      
      // Create the issue data with all required fields and proper enum values
      const issueCreateData: IssueCreateData = {
        project_id: data.issue.project_id,
        category: normalizedCategory,
        title: data.issue.title,
        description: data.issue.description,

        // Required fields - only set defaults for truly required fields
        error_code: data.issue.error_code || '',
        error_message: data.issue.error_message || '',
        system_name: data.issue.system_name ? data.issue.system_name.toUpperCase() as SystemNameEnum : SystemNameEnum.OTHERS,
        issue_type: data.issue.issue_type || 'General',
        status: data.issue.status ? data.issue.status.toUpperCase() as StatusEnum : StatusEnum.Open,

        // Optional fields - only include if user actually selected values
        ...(data.issue.impact && { impact: data.issue.impact.toUpperCase() as ImpactEnum }),
        ...(data.issue.frequency && { frequency: data.issue.frequency.toUpperCase() as FrequencyEnum }),
        ...(data.issue.jira_id && { jira_id: data.issue.jira_id }),
        ...(data.issue.jira_link && { jira_link: data.issue.jira_link }),
        ...(data.issue.hemants_view && { hemants_view: data.issue.hemants_view })
      };
      
      console.log('Normalized issue data:', JSON.stringify(issueCreateData, null, 2));
      
      console.log('Submitting issue data:', JSON.stringify(issueCreateData, null, 2));
      
      // Actually create the issue using the API
      const createdIssue = await issueApi.createIssue(issueCreateData as IssueCreateData);
      console.log("Created issue:", createdIssue);

      // If there's a solution, create it too
      if (data.solution && data.solution.solution_text) {
        // Normalize the solution category to match the issue category
        let solutionCategory = category; // Use the normalized issue category

        console.log("Solution category:", {
          original: data.solution.category,
          normalized: solutionCategory
        });

        // Create a properly typed solution object
        const solutionCreateData: SolutionCreateData = {
          issue_id: createdIssue.issue_id,
          category: solutionCategory, // Use the normalized category
          solution_text: data.solution.solution_text,
          // The provided_by field will be set by the backend based on the authenticated user
        };

        console.log("Submitting solution:", solutionCreateData);
        await solutionApi.createSolution(solutionCreateData);
      }

      // After successful submission, refresh the issues list
      await fetchIssues();

      // Close the dialog
      setIsFormDialogOpen(false);

      toast({
        title: "Success",
        description: "Issue submitted successfully",
      });
    } catch (error) {
      console.error("Error handling issue submission:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit issue",
        variant: "destructive"
      });
    }
  }

  // Pass all necessary props to IssuesList
  return (
    <div className="container mx-auto p-4 md:p-6 max-w-7xl">
      <div className="flex flex-col gap-4 md:gap-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Issues</h1>
            <p className="text-muted-foreground">Browse, search, and manage exceptions</p>
          </div>
          <Button onClick={() => setIsFormDialogOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" /> Log New Issue
          </Button>
        </div>

        <Card className="overflow-hidden">
          <CardContent className="p-0">
            <Tabs defaultValue="all" className="w-full">
              <div className="border-b px-4 pt-2">
                <TabsList className="mb-0 bg-transparent justify-start w-full">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="exceptions">Exceptions</TabsTrigger>
                  <TabsTrigger value="common-issues">Common Issues</TabsTrigger>
                  <TabsTrigger value="misc">Misc</TabsTrigger>
                  <TabsTrigger value="ignored-exceptions">Ignored Exceptions</TabsTrigger>
                  <TabsTrigger value="favorites">Favorites</TabsTrigger>

                </TabsList>
              </div>
              <div className="p-4">
                {error ? (
                  <div className="flex items-center justify-center h-full">
                    <Card className="w-full max-w-md">
                      <CardContent className="p-6">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-red-500 mb-2">
                            {error.includes('expired') ? 'Session Expired' : 'Error'}
                          </div>
                          <p className="text-muted-foreground mb-4">{error}</p>
                          <div className="space-x-4">
                            {retryCount < MAX_RETRIES ? (
                              <Button 
                                onClick={handleRetry}
                                disabled={isLoading}
                              >
                                {isLoading ? 'Retrying...' : 'Retry'}
                              </Button>
                            ) : (
                              <Button onClick={() => router.push('/auth/signin')}>
                                Sign In Again
                              </Button>
                            )}
                            <Button 
                              variant="outline" 
                              onClick={() => window.location.reload()}
                            >
                              Refresh Page
                            </Button>
                          </div>
                          {retryCount > 0 && (
                            <p className="text-sm text-muted-foreground mt-2">
                              Attempt {retryCount} of {MAX_RETRIES}
                            </p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ) : isLoading ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Loading issues...</p>
                  </div>
                ) : (
                  <>
                    {isFetchingSolutions && (
                      <div className="text-center py-2 mb-4 bg-muted rounded">
                        <p className="text-xs text-muted-foreground">Loading solutions...</p>
                      </div>
                    )}
                    <TabsContent value="all" className="m-0">
                      <IssuesList
                        filter="all"
                        category="all"
                        issues={issues}
                        onRefresh={fetchIssues}
                      />
                    </TabsContent>
                    <TabsContent value="exceptions" className="m-0">
                      <IssuesList
                        filter="all"
                        category="exceptions"
                        issues={issues.filter(issue => {
                          const category = issue?.category;
                          return category && typeof category === 'string' && 
                                 category.toUpperCase() === CategoryEnum.Exceptions.toUpperCase();
                        })}
                        onRefresh={fetchIssues}
                      />
                    </TabsContent>
                    <TabsContent value="common-issues" className="m-0">
                      <IssuesList
                        filter="all"
                        category="common-issues"
                        issues={issues.filter(issue => {
                          const category = issue?.category;
                          return category && typeof category === 'string' && 
                                 category.toUpperCase() === CategoryEnum.CommonIssues.toUpperCase();
                        })}
                        onRefresh={fetchIssues}
                      />
                    </TabsContent>
                    <TabsContent value="misc" className="m-0">
                      <IssuesList
                        filter="all"
                        category="misc"
                        issues={issues.filter(issue => {
                          const category = issue?.category;
                          return category && typeof category === 'string' && 
                                 category.toUpperCase() === CategoryEnum.Misc.toUpperCase();
                        })}
                        onRefresh={fetchIssues}
                      />
                    </TabsContent>
                    <TabsContent value="ignored-exceptions" className="m-0">
                      <IssuesList
                        filter="all"
                        category="ignored-exceptions"
                        issues={issues.filter(issue => {
                          const category = issue?.category;
                          return category && typeof category === 'string' && 
                                 category.toUpperCase() === CategoryEnum.IgnoredExceptions.toUpperCase();
                        })}
                        onRefresh={fetchIssues}
                      />
                    </TabsContent>
                    <TabsContent value="favorites" className="m-0">
                      <IssuesList
                        filter="favorites"
                        category="all"
                        issues={issues}
                        onRefresh={async () => {
                          console.log("Refreshing favorites tab...");
                          // First refresh the issues
                          await fetchIssues();
                          // Force a re-render to ensure favorites are displayed correctly
                          console.log("Refreshed favorites tab");

                          // Add a small delay to ensure the component re-renders
                          setTimeout(() => {
                            // This will trigger a state update in React, forcing a re-render
                            // Use a function to get the latest state
                            setIssues(currentIssues => [...currentIssues]);
                          }, 100);
                        }}
                      />
                    </TabsContent>
                  </>)
                }
              </div>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {projectId !== null && (
        <IssueFormDialog
          open={isFormDialogOpen}
          onOpenChange={setIsFormDialogOpen}
          onSubmit={handleSubmitIssue}
          mode="create"
          projectId={projectId} // This is now guaranteed to be a number
        />
      )}
    </div>
  )
}

export default function IssuesPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen"><LoadingSpinner /></div>}>
      <IssuesContent />
    </Suspense>
  )
}
