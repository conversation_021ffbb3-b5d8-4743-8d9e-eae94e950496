import { useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

interface IssueSummary {
  total: number
  status: Record<string, number>
  priority: Record<string, number>
}

const statusColors: Record<string, string> = {
  Open: "bg-orange-500",
  "In Progress": "bg-blue-500",
  Resolved: "bg-green-500",
  Closed: "bg-gray-400",
}

const priorityColors: Record<string, string> = {
  Critical: "bg-red-500",
  High: "bg-orange-500",
  Medium: "bg-yellow-500",
  Low: "bg-green-500",
}

export function LiveIssueTracker({ projectId }: { projectId?: number }) {
  const [data, setData] = useState<IssueSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch function
  const fetchData = () => {
    setLoading(true)
    setError(null)
    const url = projectId ? `/api/issues/summary?project_id=${projectId}` : '/api/issues/summary';
    fetch(url)
      .then((res) => {
        if (!res.ok) throw new Error("Failed to fetch issue summary")
        return res.json()
      })
      .then((data) => {
        setData(data)
        setLoading(false)
      })
      .catch((err) => {
        setError(err.message)
        setLoading(false)
      })
  }

  useEffect(() => {
    fetchData()
    // Set up auto-refresh every 8 hours (28800000 ms)
    const interval = setInterval(fetchData, 28800000)
    return () => clearInterval(interval)
  }, [projectId])

  if (loading) {
    return <div className="p-4 text-center text-muted-foreground">Loading...</div>
  }
  if (error) {
    return <div className="p-4 text-center text-destructive">{error}</div>
  }
  if (!data) {
    return <div className="p-4 text-center text-muted-foreground">No data available</div>
  }

  const { total, status, priority } = data
  const statusTotal = Object.values(status).reduce((a, b) => a + b, 0) || 1
  const statusOrder = ["Open", "In Progress", "Resolved", "Closed"]

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Total Issues</span>
          <span className="font-medium">{total}</span>
        </div>
        <div className="flex h-2 overflow-hidden rounded-full bg-secondary">
          {statusOrder.map((key) =>
            status[key] ? (
              <div
                key={key}
                className={statusColors[key] || "bg-gray-300"}
                style={{ width: `${(status[key] / statusTotal) * 100}%` }}
              />
            ) : null
          )}
        </div>
        <div className="flex justify-between text-xs text-muted-foreground">
          {statusOrder.map((key) =>
            status[key] ? (
              <div key={key} className="flex items-center gap-1">
                <div className={`h-2 w-2 rounded-full ${statusColors[key] || "bg-gray-300"}`} />
                <span>{key} ({status[key]})</span>
              </div>
            ) : null
          )}
        </div>
      </div>

      <div className="space-y-3">
        <h4 className="text-sm font-medium">By Priority</h4>
        {Object.entries(priority).map(([level, count]) => (
          <div key={level} className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Badge className={priorityColors[level] || "bg-gray-400"}>{level}</Badge>
              </div>
              <span>{count}</span>
            </div>
            <Progress value={total ? (count / total) * 100 : 0} className="h-2" />
          </div>
        ))}
      </div>
    </div>
  )
}