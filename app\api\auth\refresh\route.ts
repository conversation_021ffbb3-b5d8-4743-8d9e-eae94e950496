import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

/**
 * API route to refresh the authentication token
 * This acts as a proxy to the backend refresh token endpoint
 */
export async function POST(request: NextRequest) {
  try {
    // Forward the request to the backend refresh token endpoint
    const response = await fetch(`${API_BASE_URL}/refresh-token`, {
      method: 'POST',
      headers: {
        'Cookie': request.headers.get('cookie') || '',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error(`Token refresh failed with status ${response.status}:`, errorData);
      
      return new NextResponse(
        JSON.stringify({ 
          error: errorData.detail || `Token refresh failed with status ${response.status}` 
        }),
        { status: response.status }
      );
    }

    // Get the refreshed token data
    const refreshData = await response.json();
    
    // Create a response with the refreshed token
    const nextResponse = NextResponse.json(refreshData);
    
    // Forward any cookies from the backend response
    response.headers.forEach((value, key) => {
      if (key.toLowerCase() === 'set-cookie') {
        nextResponse.headers.append('Set-Cookie', value);
      }
    });
    
    return nextResponse;
  } catch (error) {
    console.error("Error in /api/auth/refresh:", error);
    return new NextResponse(
      JSON.stringify({ error: error instanceof Error ? error.message : "Internal server error" }),
      { status: 500 }
    );
  }
}