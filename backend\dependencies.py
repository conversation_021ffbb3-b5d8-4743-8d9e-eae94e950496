from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session, joinedload
from jose import JWTError, jwt
from typing import Optional
from datetime import datetime, timedelta, timezone
from pydantic import BaseModel
from .database import get_db
from .models import User, Admin, UserProjectRole
from .auth import get_current_user
from .config import SECRET_KEY, ALGORITHM
__all__ = ['get_current_user', 'require_admin']

# Use HTTPBearer for token authentication
oauth2_scheme = HTTPBearer()

class TokenData(BaseModel):
    email: Optional[str] = None
    

async def verify_project_access(project_id: int, 
                              db: Session = Depends(get_db),
                              current_user = Depends(get_current_user)):
    # Check if user is a global admin (optional)
    admin_check = db.query(Admin).filter(Admin.user_id == current_user.user_id).first()
    if admin_check:
        return True  # Global admins can access all projects
    
    # Check project-specific access
    access = db.query(UserProjectRole).filter(
        UserProjectRole.user_id == current_user.user_id,
        UserProjectRole.project_id == project_id
    ).first()
    
    if not access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have access to this project"
        )
    
    return access  # Return the access record which includes the role

async def get_current_user(
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(oauth2_scheme)
):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        token = credentials.credentials  # Extract the token string from credentials
        print(f"Received token: {token[:50]}...")  # Debug logging (first 50 chars)

        # Try to decode with different options for NextAuth compatibility
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        except JWTError:
            # Try with different algorithms that NextAuth might use
            try:
                payload = jwt.decode(token, SECRET_KEY, algorithms=["RS256", "HS256"])
            except JWTError:
                # Try without verification as last resort (development only)
                payload = jwt.decode(token, options={"verify_signature": False})
                print("Warning: Token signature not verified!")

        print(f"Token payload: {payload}")  # Debug logging

        # Handle NextAuth token structure - email can be in multiple fields
        email: str = payload.get("email") or payload.get("sub") or payload.get("username")
        if email is None:
            print("No email found in token payload")
            raise credentials_exception

        # Additional validation for NextAuth tokens
        if not email or ("@" not in email and not email.isdigit()):
            # If email is numeric, it might be a user ID, try to find by ID
            if email and email.isdigit():
                print(f"Token contains user ID: {email}, looking up by ID")
                with db as session:
                    user = session.query(User).options(joinedload(User.admin)).filter(User.user_id == int(email)).first()
                    if user:
                        email = user.email
                    else:
                        print(f"User not found for ID: {email}")
                        raise credentials_exception
            else:
                print(f"Invalid email format: {email}")
                raise credentials_exception

        token_data = TokenData(email=email)
    except JWTError as e:
        print(f"JWT Error: {str(e)}")  # Debug logging
        raise credentials_exception
    except Exception as e:
        print(f"Authentication error: {str(e)}")  # Debug logging
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

    with db as session:
        user = session.query(User).options(joinedload(User.admin)).filter(User.email == token_data.email).first()
        if user is None:
            print(f"User not found for email: {token_data.email}")  # Debug logging
            raise credentials_exception
        user._is_admin = user.admin is not None
        return user

def require_admin(current_user: User = Depends(get_current_user)):
    is_admin = getattr(current_user, '_is_admin', current_user.is_admin)

    if not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    return current_user