"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Loader2, PlusCircle, Users, Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useToast } from "@/components/ui/use-toast"
import { projectApi } from "@/lib/api"
import { Project } from "@/types/project"

interface ProjectWithMemberCount extends Project {
  user_count: number
}

export function ProjectsListWithUsers() {
  const [projects, setProjects] = useState<ProjectWithMemberCount[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const { toast } = useToast()

  // Fetch projects from API
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setIsLoading(true)
        const response = await projectApi.getProjectsWithUsers()
        
        // Handle different possible response shapes
        let projectsArray: ProjectWithMemberCount[] = []
        
        if (Array.isArray(response)) {
          projectsArray = response as ProjectWithMemberCount[]
        } else if (response && typeof response === 'object' && 'projects' in response) {
          projectsArray = (response as { projects: ProjectWithMemberCount[] }).projects || []
        }
        
        setProjects(projectsArray)
      } catch (error) {
        console.error('Error fetching projects:', error)
        toast({
          title: 'Error',
          description: 'Failed to load projects',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchProjects()
  }, [toast])

  const filteredProjects = projects.filter(project => {
    const searchLower = searchQuery.toLowerCase()
    return (
      project.name.toLowerCase().includes(searchLower) ||
      (project.description?.toLowerCase() || '').includes(searchLower) ||
      project.code?.toLowerCase().includes(searchLower) ||
      project.status?.toLowerCase().includes(searchLower)
    )
  })

  const getStatusVariant = (status: string = 'inactive') => {
    const statusLower = status.toLowerCase()
    if (statusLower.includes('active')) return 'default'
    if (statusLower.includes('inactive')) return 'secondary'
    if (statusLower.includes('archive')) return 'outline'
    return 'default'
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="space-y-1">
          <CardTitle className="text-2xl font-bold">Projects</CardTitle>
          <p className="text-sm text-muted-foreground">
            Manage your projects and their members
          </p>
        </div>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          New Project
        </Button>
      </CardHeader>
      <CardContent>
        <div className="flex items-center py-4">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search projects..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Code</TableHead>
                <TableHead>Description</TableHead>
                <TableHead className="text-center">Members</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProjects.length > 0 ? (
                filteredProjects.map((project) => (
                  <TableRow key={project.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {project.icon_name && (
                          <span className="text-lg">{project.icon_name}</span>
                        )}
                        <span>{project.name}</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {project.code || '-'}
                    </TableCell>
                    <TableCell className="text-muted-foreground max-w-[200px] truncate">
                      {project.description || 'No description'}
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        <Users className="h-4 w-4 mr-1" />
                        {project.user_count || 0}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusVariant(project.status)}>
                        {project.status || 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {project.created_at ? new Date(project.created_at).toLocaleDateString() : '-'}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    {searchQuery ? 'No matching projects found' : 'No projects available'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
