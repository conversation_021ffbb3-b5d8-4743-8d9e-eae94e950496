import { getSession } from 'next-auth/react';
import { ApiError, ApiErrorResponse } from './types';

// API Configuration
// Use Next.js API routes instead of calling backend directly
export const API_BASE_URL = typeof window !== 'undefined'
  ? `${window.location.origin}/api`
  : '/api';
const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  Accept: 'application/json',
};

// Request Interceptor
const requestInterceptor = async (config: RequestInit) => {
  const session = await getSession();
  const headers = new Headers(DEFAULT_HEADERS);
  
  if (session?.user?.accessToken) {
    headers.set('Authorization', `Bearer ${session.user.accessToken}`);
  }

  return {
    ...config,
    headers: {
      ...Object.fromEntries(headers.entries()),
      ...(config.headers || {}),
    },
    credentials: 'include' as const,
  };
};

// Response Interceptor
const responseInterceptor = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    let errorData: ApiErrorResponse;
    try {
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        errorData = await response.json();
      } else {
        // Handle HTML error responses (like 404 pages)
        const htmlText = await response.text();
        console.error('Received HTML response instead of JSON:', htmlText.substring(0, 200));
        errorData = {
          detail: `Server returned HTML instead of JSON. This usually means the API endpoint doesn't exist or the backend server is not running. Status: ${response.status}`,
          status_code: response.status,
        };
      }
    } catch {
      errorData = {
        detail: `Failed to parse error response. Status: ${response.status} ${response.statusText}`,
        status_code: response.status,
      };
    }

    throw new ApiError(
      errorData.detail || response.statusText,
      response.status,
      errorData
    );
  }

  // Handle empty responses (like 204 No Content)
  if (response.status === 204) {
    return undefined as unknown as T;
  }

  // Check if response is JSON before parsing
  const contentType = response.headers.get('content-type');
  if (!contentType || !contentType.includes('application/json')) {
    const textResponse = await response.text();
    console.error('Expected JSON but received:', textResponse.substring(0, 200));
    throw new ApiError(
      'Server returned non-JSON response. This usually means the backend server is not running or there\'s a configuration issue.',
      response.status
    );
  }

  return response.json();
};

// Token Refresh Handler
let isRefreshing = false;
let refreshPromise: Promise<string | null> | null = null;

interface SessionWithRefreshToken {
  user: {
    id: string;
    name: string | null;
    email: string | null;
    image: string | null;
    accessToken: string;
    refreshToken: string;
    role: string;
    is_admin: boolean;
  };
}

const refreshToken = async (): Promise<string | null> => {
  if (isRefreshing) {
    return refreshPromise;
  }

  isRefreshing = true;
  refreshPromise = (async () => {
    try {
      const session = await getSession() as SessionWithRefreshToken | null;
      const refreshToken = session?.user?.refreshToken;
      
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: DEFAULT_HEADERS,
        credentials: 'include',
        body: JSON.stringify({
          refresh_token: refreshToken,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const data = await response.json();
      
      // Update session with new tokens
      await fetch('/api/auth/session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          accessToken: data.access_token,
          refreshToken: data.refresh_token || refreshToken,
        }),
      });

      return data.access_token;
    } catch (error) {
      console.error('Token refresh failed:', error);
      // Redirect to login on refresh failure
      if (typeof window !== 'undefined') {
        const currentProject = new URLSearchParams(window.location.search).get('project');
        const loginUrl = currentProject
          ? `/auth/login?project=${currentProject}`
          : '/auth/login';
        window.location.href = loginUrl;
      }
      return null;
    } finally {
      isRefreshing = false;
      refreshPromise = null;
    }
  })();

  return refreshPromise;
};

// Core API Client
const apiClient = async <T>(
  endpoint: string,
  options: ApiRequestInit = {}
): Promise<T> => {
  try {
    const { transformResponse, ...requestOptions } = options;
    const config = await requestInterceptor(requestOptions);
    
    // Check if endpoint is already a full URL
    let fullUrl: string;
    try {
      const url = new URL(endpoint);
      fullUrl = url.toString();
    } catch {
      // If URL constructor throws, it's not a full URL, so prepend the base URL
      fullUrl = `${API_BASE_URL}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`;
    }
    
    console.log('API Request:', fullUrl);
    let response = await fetch(fullUrl, config);

    // Handle token refresh on 401
    if (response.status === 401 && !endpoint.includes('/auth/')) {
      const newToken = await refreshToken();
      if (newToken) {
        // Retry the request with the new token
        const newConfig = await requestInterceptor({
          ...options,
          headers: {
            ...options.headers,
            'Authorization': `Bearer ${newToken}`
          }
        });
        response = await fetch(fullUrl, newConfig);
      }
    }

    // Use custom transform if provided
    if (transformResponse) {
      return transformResponse(response);
    }
    
    // Handle 204 No Content responses
    if (response.status === 204) {
      return undefined as unknown as T;
    }
    
    return responseInterceptor<T>(response);
  } catch (error) {
    if (error instanceof ApiError) throw error;
    throw new ApiError(
      error instanceof Error ? error.message : 'An unknown error occurred',
      0
    );
  }
};

// Extend RequestInit to include params and transformResponse
type TransformResponse = (response: Response) => Promise<any>;

interface ApiRequestInit extends Omit<RequestInit, 'params'> {
  params?: Record<string, any> | undefined;
  transformResponse?: (response: Response) => any;
}

// API Methods
export const api = {
  get: <T>(endpoint: string, options: ApiRequestInit = {}) => {
    const { params, ...restOptions } = options;

    // Build URL string - handle both relative and absolute URLs
    let urlString: string;

    if (API_BASE_URL.startsWith('http')) {
      // Absolute URL - build manually to avoid URL constructor issues with relative endpoints
      urlString = `${API_BASE_URL}${endpoint}`;

      if (params) {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, String(value));
          }
        });

        if (searchParams.toString()) {
          const separator = urlString.includes('?') ? '&' : '?';
          urlString += separator + searchParams.toString();
        }
      }
    } else {
      // Relative URL - build manually
      urlString = `${API_BASE_URL}${endpoint}`;

      if (params) {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, String(value));
          }
        });

        if (searchParams.toString()) {
          const separator = urlString.includes('?') ? '&' : '?';
          urlString += separator + searchParams.toString();
        }
      }
    }

    console.log('Final API URL:', urlString);
    return apiClient<T>(urlString, { ...restOptions, method: 'GET' });
  },

  post: <T>(endpoint: string, data?: any, options: ApiRequestInit = {}) => {
    const body = data ? JSON.stringify(data) : null;
    return apiClient<T>(endpoint, {
      ...options,
      method: 'POST',
      body,
    });
  },

  put: <T>(endpoint: string, data: any, options: ApiRequestInit = {}) =>
    apiClient<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  patch: <T>(endpoint: string, data: any, options: ApiRequestInit = {}) =>
    apiClient<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: JSON.stringify(data),
    }),

  delete: <T = void>(endpoint: string, options: ApiRequestInit = {}) =>
    apiClient<T>(endpoint, { ...options, method: 'DELETE' }),
};

export default api;
