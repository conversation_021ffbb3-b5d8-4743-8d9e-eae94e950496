import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow-md",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md",
        outline:
          "border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-accent",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm hover:shadow-md",
        ghost: "hover:bg-accent/20 hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline hover:text-primary/80",
        animated: "flex w-[200px] h-[50px] bg-primary rounded-[40px] shadow-md hover:shadow-lg justify-between items-center border-none cursor-pointer group"
      },
      size: {
        default: "h-8 px-4 py-2",
        sm: "h-7 rounded-md px-3 py-1.5 text-xs",
        lg: "h-10 rounded-md px-6 py-2.5 text-sm",
        icon: "h-8 w-8 p-1.5",
      },
      position: {
        default: "",
        topRight: "fixed top-4 right-4 z-50",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  position?: "default" | "topRight";
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, position = "default", asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, position, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }

export const AnimatedButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <Button
        variant="animated"
        className={cn(className)}
        ref={ref}
        {...props}
      >
        <span className="flex items-center justify-between w-full">
          <span className="w-[calc(200px-55px)] h-full flex items-center justify-center text-white text-[1.125rem] tracking-[1.2px]">
            {children}
          </span>
          <span className="w-[55px] h-[55px] bg-[#f59aff] flex items-center justify-center rounded-full border-[3px] border-[#1d2129] group-hover:animate-arrow">
            <svg
              width={16}
              height={19}
              viewBox="0 0 16 19"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="transition-all duration-1500"
            >
              <circle cx="1.61321" cy="1.61321" r="1.5" fill="black" />
              <circle cx="5.73583" cy="1.61321" r="1.5" fill="black" />
              <circle cx="5.73583" cy="5.5566" r="1.5" fill="black" />
              <circle cx="9.85851" cy="5.5566" r="1.5" fill="black" />
              <circle cx="9.85851" cy="9.5" r="1.5" fill="black" />
              <circle cx="13.9811" cy="9.5" r="1.5" fill="black" />
              <circle cx="5.73583" cy="13.4434" r="1.5" fill="black" />
              <circle cx="9.85851" cy="13.4434" r="1.5" fill="black" />
              <circle cx="1.61321" cy="17.3868" r="1.5" fill="black" />
              <circle cx="5.73583" cy="17.3868" r="1.5" fill="black" />
            </svg>
          </span>
        </span>
      </Button>
    )
  }
)
AnimatedButton.displayName = "AnimatedButton"
