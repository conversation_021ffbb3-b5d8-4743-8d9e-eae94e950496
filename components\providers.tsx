'use client'

import { SessionProvider } from "next-auth/react"
import { UserProvider } from "@/contexts/user-context"
import { ThemeProvider } from "@/components/theme-provider"
import { SidebarProvider } from "@/components/sidebar-provider"
import { Toaster } from "@/components/ui/toaster"
import { ReactNode } from "react"

export default function Providers({ children }: { children: ReactNode }) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
      <SessionProvider>
        <UserProvider>
          <SidebarProvider>
            {children}
            <Toaster />
          </SidebarProvider>
        </UserProvider>
      </SessionProvider>
    </ThemeProvider>
  )
}
