/**
 * Utility functions for public API endpoints that don't require authentication
 */

/**
 * Get public projects list
 * This is used on the homepage before authentication
 */
export async function getPublicProjects() {
  try {
    const response = await fetch('/api/public/projects');

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Failed to fetch projects: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching public projects:', error);
    throw error;
  }
}
