/**
 * Security headers configuration for Next.js middleware
 * This file contains all security-related headers and their configurations
 */

/**
 * Content Security Policy (CSP) configuration
 * Adjust these directives based on your application's requirements
 */
const csp = {
  // Default policy for loading content
  defaultSrc: ["'self'"],
  // Define valid sources of JavaScript
  scriptSrc: [
    "'self'",
    "'unsafe-inline'", // Required for Next.js
    "'unsafe-eval'",   // Required for Next.js in development
    "https://cdn.jsdelivr.net",
  ],
  // Define valid sources of styles
  styleSrc: [
    "'self'",
    "'unsafe-inline'", // Required for some libraries
    "https://fonts.googleapis.com",
  ],
  // Define valid sources of images
  imgSrc: ["'self'", "data:", "https:", "http:"],
  // Define valid sources of fonts
  fontSrc: ["'self'", "https://fonts.gstatic.com"],
  // Define valid sources for XHR, WebSocket, and EventSource connections
  connectSrc: [
    "'self'",
    "https://*.sentry.io",
    // Add your API endpoints here
  ],
  // Prevent clickjacking
  frameAncestors: ["'none'"],
  // Prevent form submission to unauthorized locations
  formAction: ["'self'"],
  // Prevent <base> tag manipulation
  baseUri: ["'self'"],
  // Prevent loading of any plugins
  objectSrc: ["'none'"],
};

/**
 * Generate a Content Security Policy string from the configuration
 */
const generateCSP = () => {
  return Object.entries(csp)
    .map(([key, values]) => {
      return `${key} ${values.join(' ')}`;
    })
    .join('; ');
};

/**
 * Security headers configuration
 */
export const securityHeaders = {
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Prevent clickjacking
  'X-Frame-Options': 'DENY',
  
  // Enable XSS filtering (if supported by the user's browser)
  'X-XSS-Protection': '1; mode=block',
  
  // Content Security Policy
  'Content-Security-Policy': generateCSP(),
  
  // Feature Policy (for older browsers)
  'Permissions-Policy': [
    'camera=()',
    'geolocation=()',
    'microphone=()',
    'payment=()',
    'fullscreen=(self)',
  ].join(', '),
  
  // Control referrer information
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // HTTP Strict Transport Security
  'Strict-Transport-Security': 'max-age=63072000; includeSubDomains; preload',
  
  // Cross-Origin policies
  'Cross-Origin-Embedder-Policy': 'require-corp',
  'Cross-Origin-Opener-Policy': 'same-origin',
  'Cross-Origin-Resource-Policy': 'same-site',
  
  // Disable DNS prefetching
  'X-DNS-Prefetch-Control': 'off',
  
  // Disable IE compatibility mode
  'X-UA-Compatible': 'IE=edge',
};

// Note: Middleware configuration has been moved to middleware.ts
// to ensure it's statically analyzable at build time.
