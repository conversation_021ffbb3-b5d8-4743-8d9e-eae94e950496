import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

// Search parameters are now handled directly in the GET function

export async function GET(request: NextRequest) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user || !session.user.accessToken) {
      console.error('No authenticated session found');
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    console.log('Authenticated user:', session.user.email);

    // Get the search parameters
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const projectIdParam = searchParams.get('project_id') || '';
    const type = searchParams.get('type') || 'all';
    
    console.log('=== New Search Request ===');
    console.log(`Query: ${query}`);
    console.log(`Project ID: ${projectIdParam}`);
    console.log(`Type: ${type}`);

    // Convert projectId to number if provided, otherwise set to 0
    const projectId = projectIdParam ? parseInt(projectIdParam, 10) : 0;

    // Prepare the results array
    let results: any[] = [];
    
    // Get the access token from the session
    const token = session.user.accessToken;

    // Search issues if type is 'all' or 'issues'
    if (type === 'all' || type === 'issues') {
      try {
        console.log('\n--- Searching Issues ---');
        console.log(`Query: ${query}`);
        console.log(`Project ID: ${projectId}`);
        
        // Determine the correct endpoint based on whether projectId is provided
        let issuesEndpoint = '';

        if (projectId && projectId > 0) {
          // If project ID is provided and valid, search within that project
          issuesEndpoint = `${API_BASE_URL}/projects/${projectId}/issues?query=${encodeURIComponent(query)}`;
        } else {
          // Otherwise, search across all projects
          issuesEndpoint = `${API_BASE_URL}/issues?query=${encodeURIComponent(query)}`;
        }

        console.log('\n--- Fetching Issues ---');
        console.log('Issues Endpoint:', issuesEndpoint);
        
        const issuesResponse = await fetch(
          issuesEndpoint,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            cache: 'no-store' // Ensure we don't get cached results
          }
        );

        console.log('Issues Response Status:', issuesResponse.status);
        
        if (issuesResponse.ok) {
          const issuesData = await issuesResponse.json();
          console.log('Raw Issues API Response:', JSON.stringify(issuesData, null, 2));
          
          if (!issuesData || !issuesData.items) {
            console.warn('No items found in issues response or invalid response format');
          } else {
            console.log(`Found ${issuesData.items.length} issues`);
            
            // Map the issues to the expected format
            const issueResults = issuesData.items.map((issue: any) => {
              console.log(`\nProcessing Issue ID: ${issue.issue_id}`);
              console.log(`Title: ${issue.title}`);
              console.log(`Matches search? ${issue.title.toLowerCase().includes(query.toLowerCase()) ? 'YES' : 'NO'}`);
              
              return {
                id: issue.issue_id,
                title: issue.title,
                description: issue.error_message,
                type: 'issue',
                _debug: {
                  matchesSearch: issue.title.toLowerCase().includes(query.toLowerCase())
                }
              };
            });
            
            console.log('\n--- Mapped Issue Results ---');
            console.log(JSON.stringify(issueResults, null, 2));
            
            // Filter out non-matching results if needed
            const matchingResults = issueResults.filter((issue: any) => 
              issue.title.toLowerCase().includes(query.toLowerCase())
            );
            
            console.log(`\n--- Matching Issues (${matchingResults.length} of ${issueResults.length}) ---`);
            matchingResults.forEach((issue: any) => {
              console.log(`- ${issue.title} (ID: ${issue.id})`);
            });
            
            results = [...results, ...matchingResults];
          }
        } else {
          const errorText = await issuesResponse.text();
          console.error('Error fetching issues:', {
            status: issuesResponse.status,
            statusText: issuesResponse.statusText,
            error: errorText
          });
        }
      } catch (error) {
        console.error('Error searching issues:', error);
      }
    }

    // Search knowledge base if type is 'all' or 'knowledge'
    if (type === 'all' || type === 'knowledge') {
      try {
        console.log('Searching knowledge base with query:', query, 'in project:', projectId);
        // Determine the correct endpoint based on whether projectId is provided
        let knowledgeEndpoint = '';

        if (projectId && projectId > 0) {
          // If project ID is provided and valid, search within that project
          knowledgeEndpoint = `${API_BASE_URL}/projects/${projectId}/knowledge-base?query=${encodeURIComponent(query)}`;
        } else {
          // Otherwise, search across all projects
          knowledgeEndpoint = `${API_BASE_URL}/knowledge-base?query=${encodeURIComponent(query)}`;
        }

        const knowledgeResponse = await fetch(
          knowledgeEndpoint,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (knowledgeResponse.ok) {
          const knowledgeData = await knowledgeResponse.json();
          // Map the knowledge base entries to the expected format
          const knowledgeResults = knowledgeData.items?.map((kb: any) => ({
            id: kb.kb_id,
            title: kb.title,
            description: kb.content?.substring(0, 150) + '...',
            type: 'knowledge'
          })) || [];

          results = [...results, ...knowledgeResults];
        }
      } catch (error) {
        console.error('Error searching knowledge base:', error);
      }
    }

    // Return the combined results
    return NextResponse.json({ items: results });
  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}
