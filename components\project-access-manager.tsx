'use client'

import { useState } from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { AccessLevel } from "@/types/access"
import { useUser } from "@/contexts/user-context"

interface ProjectAccessManagerProps {
  projectId: number;
  userId: number;
  currentAccess?: AccessLevel;
}

export function ProjectAccessManager({ projectId, userId, currentAccess }: ProjectAccessManagerProps) {
  const [access, setAccess] = useState<AccessLevel>(currentAccess || AccessLevel.READ)
  const [isLoading, setIsLoading] = useState(false)
  const { user } = useUser()

  if (!user?.is_admin) return null

  const handleUpdateAccess = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/projects/${projectId}/access`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
          access_level: access,
        }),
      })

      if (!response.ok) throw new Error('Failed to update access')

      toast({
        title: "Access Updated",
        description: "Project access has been updated successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update project access.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center gap-2">
      <Select value={access} onValueChange={(value) => setAccess(value as AccessLevel)}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select access level" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={AccessLevel.READ}>Read</SelectItem>
          <SelectItem value={AccessLevel.WRITE}>Write</SelectItem>
          <SelectItem value={AccessLevel.ADMIN}>Admin</SelectItem>
        </SelectContent>
      </Select>
      <Button onClick={handleUpdateAccess} disabled={isLoading}>
        {isLoading ? "Updating..." : "Update Access"}
      </Button>
    </div>
  )
}