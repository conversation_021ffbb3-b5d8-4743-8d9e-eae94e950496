"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button"
import { Loader } from "@/components/ui/loader"
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"
import {ArrowRight,Box,Warehouse,Cpu,Sparkles,Database,Code,Server,Shield,Zap,ChevronRight,BarChart3,Activity,Layers,CheckCircle2,} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useEffect, useState } from "react"
import { getPublicProjects } from "@/utils/public-api"

interface Project {
  project_id: number
  project_name: string
  description: string
  icon_name: string
}

const iconMap = {
  box: Box,
  warehouse: Warehouse,
  cpu: Cpu,
  sparkles: Sparkles,
  database: Database,
  code: Code,
  server: Server,
  shield: Shield,
  zap: Zap,
} as const

const features = [
  {
    title: "Exception Logging",
    description: "Capture and log all exceptions in one centralized location",
    icon: Database,
  },
  {
    title: "Solution Repository",
    description: "Store and retrieve solutions for common exceptions",
    icon: Code,
  },
  {
    title: "Knowledge Base",
    description: "Build a comprehensive knowledge base for your team",
    icon: Server,
  },
  {
    title: "Real-time Alerts",
    description: "Get notified instantly when critical exceptions occur",
    icon: Activity,
  },
]

export default function HomePage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeProject, setActiveProject] = useState<number | null>(null)
  const [activeTab, setActiveTab] = useState("features")

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const data = await getPublicProjects()
        setProjects(data)
        if (data.length > 0) {
          setActiveProject(0)
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to load projects";
        setError(errorMessage);
        console.error("Error fetching projects:", err)
      } finally {
        setTimeout(() => setIsLoading(false), 600)
      }
    }

    fetchProjects()
  }, [])

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-background via-background/95 to-background/90">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-[30%] -left-[10%] w-[70%] h-[70%] rounded-full bg-primary/5 blur-3xl" />
        <div className="absolute -bottom-[20%] -right-[10%] w-[60%] h-[60%] rounded-full bg-primary/5 blur-3xl" />
        <div className="absolute top-[40%] right-[5%] w-[40%] h-[40%] rounded-full bg-primary/3 blur-3xl" />
        <div className="absolute bottom-[30%] left-[5%] w-[30%] h-[30%] rounded-full bg-primary/3 blur-3xl" />

        {/* Grid pattern overlay */}
        <div className="absolute inset-0 bg-[url('/placeholder.svg?height=20&width=20')] bg-[length:40px_40px] opacity-[0.015]" />
      </div>

      {/* Header with Get Started button */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border/30">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            <span className="font-semibold text-lg">ExTrack</span>
          </div>

          <div className="hidden md:flex items-center gap-8">
            <Link href="#features" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Features
            </Link>
            <Link href="#projects" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Projects
            </Link>
            <Link href="#about" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              About
            </Link>
          </div>

          <Button asChild variant="default" size="lg">
            <Link href={activeProject !== null ? `/auth/login?project=${projects[activeProject]?.project_id}` : "#projects"}>
              Get Started
            </Link>
          </Button>
        </div>
      </header>

      <div className="container relative mx-auto px-6 pt-24 min-h-screen z-10">
        <AnimatePresence>
          {isLoading ? (
            <LoadingState />
          ) : error ? (
            <ErrorState error={error} />
          ) : (
            <ContentState
              projects={projects}
              activeProject={activeProject}
              setActiveProject={setActiveProject}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

function LoadingState() {
  return (
    <motion.div
      key="loading"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="absolute inset-0 flex flex-col items-center justify-center"
    >
      <Loader />
    </motion.div>
  )
}

function ErrorState({ error }: { error: string }) {
  return (
    <motion.div
      key="error"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center py-10 flex flex-col items-center max-w-md mx-auto"
    >
      <motion.div
        className="rounded-full bg-destructive/10 p-6 mb-6 border border-destructive/20 shadow-lg"
        initial={{ rotate: -10 }}
        animate={{ rotate: [0, -5, 0, 5, 0] }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Shield className="h-10 w-10 text-destructive" />
      </motion.div>

      <motion.h3
        className="text-xl font-semibold text-destructive mb-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        Connection Error
      </motion.h3>

      <motion.p
        className="text-muted-foreground mb-6"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        {error}
      </motion.p>

      <Button
        variant="default"
        className="px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300"
        onClick={() => window.location.reload()}
      >
        <span className="mr-2">Try Again</span>
        <Zap className="h-4 w-4" />
      </Button>
    </motion.div>
  )
}

function ContentState({
  projects,
  activeProject,
  setActiveProject,
  activeTab,
  setActiveTab,
}: {
  projects: Project[]
  activeProject: number | null
  setActiveProject: (index: number) => void
  activeTab: string
  setActiveTab: (tab: string) => void
}) {
  return (
    <motion.div key="content" initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="w-full">
      {/* Hero section with split layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center py-16 md:py-24">
        {/* Left column - Hero content */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          className="flex flex-col"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.8, type: "spring" }}
            className="inline-flex items-center gap-2 px-5 py-2 mb-6 rounded-full bg-primary/10 text-primary border border-primary/20 shadow-sm self-start"
          >
            <Sparkles className="h-4 w-4" />
            <span className="text-sm font-medium">Exception Management Platform</span>
          </motion.div>

          <motion.h1
            className="text-4xl sm:text-5xl font-bold tracking-tight md:text-6xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent animate-gradient relative inline-block mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.8 }}
          >
            Welcome to ExTrack
            <div className="absolute -bottom-2 left-1/2 -translate-x-1/2 w-1/2 h-1 bg-gradient-to-r from-transparent via-primary/50 to-transparent rounded-full blur-sm"></div>
          </motion.h1>

          <motion.p
            className="text-base sm:text-lg md:text-xl leading-relaxed text-muted-foreground mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            Your centralized platform for tracking, managing, and resolving exceptions efficiently. Streamline your
            development workflow and improve application reliability.
          </motion.p>

          <motion.div
            className="flex flex-wrap gap-4 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.8 }}
          >
            <Button asChild variant="default" size="lg">
              <Link href={activeProject !== null ? `/auth/login?project=${projects[activeProject]?.project_id}` : "#projects"}>
                Get Started
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="gap-2 px-8">
              <Link href="#projects">
                View Projects
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </motion.div>


        </motion.div>

        {/* Right column - Illustration/Animation */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="relative flex justify-center items-center"
        >
          <div className="relative w-full max-w-md aspect-square">
            {/* Decorative elements */}
            <motion.div
              className="absolute inset-0 rounded-full bg-primary/5 blur-xl"
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 4, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
            />

            {/* Main illustration */}
            <div className="relative z-10 w-full h-full flex items-center justify-center">
              <div className="relative w-4/5 h-4/5">
                {/* Central icon */}
                <motion.div
                  className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-primary/10 rounded-xl border border-primary/20 shadow-lg flex items-center justify-center"
                  animate={{ rotate: [0, 5, 0, -5, 0] }}
                  transition={{ duration: 10, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
                >
                  <Sparkles className="h-10 w-10 text-primary" />
                </motion.div>

                {/* Orbiting elements */}
                {[Database, Code, Server, Shield].map((Icon, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-16 h-16 bg-background rounded-lg border border-border shadow-md flex items-center justify-center"
                    style={{
                      top: `${50 + 35 * Math.sin((i * Math.PI) / 2)}%`,
                      left: `${50 + 35 * Math.cos((i * Math.PI) / 2)}%`,
                      transform: "translate(-50%, -50%)",
                    }}
                    animate={{
                      x: [0, 10, 0, -10, 0],
                      y: [0, -10, 0, 10, 0],
                      rotate: [0, 10, 0, -10, 0],
                    }}
                    transition={{
                      duration: 10,
                      repeat: Number.POSITIVE_INFINITY,
                      ease: "easeInOut",
                      delay: i * 0.5,
                    }}
                  >
                    <Icon className="h-6 w-6 text-primary" />
                  </motion.div>
                ))}

                {/* Connecting lines */}
                <svg className="absolute inset-0 w-full h-full" style={{ zIndex: -1 }}>
                  <motion.path
                    d="M 50,50 L 85,15 M 50,50 L 85,85 M 50,50 L 15,85 M 50,50 L 15,15"
                    stroke="currentColor"
                    strokeWidth="1"
                    strokeDasharray="5,5"
                    className="text-primary/30"
                    strokeLinecap="round"
                    fill="none"
                    animate={{
                      opacity: [0.3, 0.6, 0.3],
                      pathLength: [0.8, 1, 0.8],
                    }}
                    transition={{ duration: 8, repeat: Number.POSITIVE_INFINITY }}
                  />
                </svg>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Tabs section */}
      <motion.div
        id="features"
        className="py-16 scroll-mt-20"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-4 px-4 py-1 border-primary/20 bg-primary/5">
            <Layers className="h-4 w-4 mr-2 text-primary" />
            Platform Overview
          </Badge>
          <h2 className="text-3xl font-bold mb-4">Everything you need to manage exceptions</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            ExTrack provides a comprehensive suite of tools to help you track, manage, and resolve exceptions
            efficiently.
          </p>
        </div>

        <Tabs
          defaultValue="features"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full max-w-4xl mx-auto"
        >
          <TabsList className="grid grid-cols-2 w-full max-w-md mx-auto mb-8">
            <TabsTrigger value="features">Features</TabsTrigger>
            <TabsTrigger value="workflow">Workflow</TabsTrigger>
          </TabsList>

          <TabsContent value="features" className="focus-visible:outline-none focus-visible:ring-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {features.map((feature, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.1, duration: 0.5 }}
                  className="flex gap-4 p-6 rounded-xl border border-border/50 bg-card/50"
                >
                  <div className="shrink-0 p-3 rounded-lg bg-primary/10 h-fit">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium text-lg mb-2">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="workflow" className="focus-visible:outline-none focus-visible:ring-0">
            <div className="relative p-6 rounded-xl border border-border/50 bg-card/50">
              <div className="flex flex-col md:flex-row gap-8 items-center">
                <div className="w-full md:w-1/2">
                  <ol className="relative border-l border-primary/30 ml-3 space-y-8">
                    {[
                      { title: "Log Exceptions", description: "Automatically capture and log all exceptions" },
                      { title: "Analyze Patterns", description: "Identify recurring issues and patterns" },
                      { title: "Implement Solutions", description: "Apply and document effective solutions" },
                      { title: "Monitor Results", description: "Track resolution effectiveness over time" },
                    ].map((step, i) => (
                      <li key={i} className="ml-6">
                        <span className="absolute flex items-center justify-center w-6 h-6 bg-primary/20 rounded-full -left-3 border border-primary/30 text-xs font-medium text-primary">
                          {i + 1}
                        </span>
                        <h3 className="font-medium text-lg">{step.title}</h3>
                        <p className="text-sm text-muted-foreground">{step.description}</p>
                      </li>
                    ))}
                  </ol>
                </div>

                <div className="w-full md:w-1/2 flex justify-center">
                  <div className="relative w-full max-w-xs aspect-square">
                    <motion.div
                      className="absolute inset-0 rounded-full bg-primary/5"
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 4, repeat: Number.POSITIVE_INFINITY }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 30, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                        className="w-full h-full"
                      >
                        <svg viewBox="0 0 100 100" className="w-full h-full">
                          <circle
                            cx="50"
                            cy="50"
                            r="45"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="0.5"
                            className="text-primary/20"
                          />
                          <circle cx="50" cy="5" r="5" fill="currentColor" className="text-primary/60" />
                          <circle cx="50" cy="95" r="5" fill="currentColor" className="text-primary/60" />
                          <circle cx="5" cy="50" r="5" fill="currentColor" className="text-primary/60" />
                          <circle cx="95" cy="50" r="5" fill="currentColor" className="text-primary/60" />
                        </svg>
                      </motion.div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-20 h-20 rounded-full bg-primary/10 border border-primary/30 flex items-center justify-center">
                          <Activity className="h-8 w-8 text-primary" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </motion.div>

      {/* Project carousel section */}
      <motion.div
        id="projects"
        className="py-16 scroll-mt-20"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-4 px-4 py-1 border-primary/20 bg-primary/5">
            <Layers className="h-4 w-4 mr-2 text-primary" />
            Your Projects
          </Badge>
          <h2 className="text-3xl font-bold mb-4">Select a project to continue</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Choose from your existing projects or create a new one to start tracking exceptions.
          </p>
        </div>

        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full max-w-6xl mx-auto"
        >
          <CarouselContent className="py-4">
            {projects.map((project, index) => (
              <CarouselItem
                key={project.project_id}
                className="basis-full sm:basis-1/2 lg:basis-1/3 pl-2 pr-2 md:pl-4 md:pr-4"
              >
                <div
                  className={`h-full ${activeProject === index ? "scale-100" : "scale-90"} transition-all duration-300`}
                  onClick={() => {
                    if (project.project_id) {
                      setActiveProject(index);
                    }
                  }}
                >
                  <ProjectCard
                    title={project.project_name}
                    description={project.description}
                    icon={React.createElement(iconMap[project.icon_name as keyof typeof iconMap] || Box, {
                      className: "h-10 w-10",
                    })}
                    id={project.project_id.toString()}
                    index={index}
                    isActive={activeProject === index}
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="flex justify-center mt-4 gap-2">
            <CarouselPrevious className="static translate-y-0 h-9 w-9" />
            <CarouselNext className="static translate-y-0 h-9 w-9" />
          </div>
        </Carousel>

        {/* CTA section */}
        <motion.div
          className="flex justify-center mt-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Button
            asChild
            size="lg"
            variant="default"
            className="px-8 py-6 font-medium shadow-md hover:shadow-lg transition-all duration-300"
          >
            <Link
              href={
                activeProject !== null && projects[activeProject]?.project_id ? `/auth/login?project=${projects[activeProject].project_id}` : "/auth/login"
              }
            >
              Continue with Selected Project
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </motion.div>
      </motion.div>

      {/* Footer section */}
      <footer id="about" className="py-12 mt-12 border-t border-border/30 scroll-mt-20">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <div className="flex items-center gap-2 mb-4">
              <Sparkles className="h-5 w-5 text-primary" />
              <span className="font-semibold text-lg">ExTrack</span>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Your centralized platform for tracking, managing, and resolving exceptions efficiently.
            </p>
          </div>

          <div>
            <h3 className="font-medium mb-4">Quick Links</h3>
            <div className="grid gap-2">
              <Link href="#features" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Features
              </Link>
              <Link href="#projects" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                Projects
              </Link>
              <Link
                href={activeProject !== null ? `/auth/login?project=${projects[activeProject]?.project_id}` : "#projects"}
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                Login
              </Link>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-4">Contact</h3>
            <div className="grid gap-2">
              <a
                href="mailto:<EMAIL>"
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                <EMAIL>
              </a>
              <a
                href="tel:+919876543210"
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                +91 9876543210
              </a>
            </div>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-border/30 flex flex-col sm:flex-row justify-between items-center gap-4">
          <p className="text-sm text-muted-foreground">© {new Date().getFullYear()} ExTrack. All rights reserved.</p>
          <div className="flex gap-4">
            <Link href="#" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Privacy Policy
            </Link>
            <Link href="#" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Terms of Service
            </Link>
          </div>
        </div>
      </footer>
    </motion.div>
  )
}

function ProjectCard({
  title,
  description,
  icon,
  id,
  index,
  isActive = false,
}: {
  title: string
  description: string
  icon: React.ReactNode
  id: string
  index: number
  isActive?: boolean
}) {
  return (
    <Card
      className={`group h-full flex flex-col transition-all duration-300 ease-out
      border ${isActive ? "border-primary/50" : "border-border/40"}
      bg-card/90 backdrop-blur-md
      ${isActive ? "shadow-lg shadow-primary/10" : "shadow-sm"}
      relative overflow-hidden`}
    >
      {/* Decorative gradient background */}
      <div
        className={`absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent ${isActive ? "opacity-100" : "opacity-0"} transition-opacity duration-300 pointer-events-none`}
      />

      {/* Subtle corner accents */}
      <div
        className={`absolute top-0 left-0 w-16 h-16 ${isActive ? "opacity-100" : "opacity-0"} transition-opacity duration-300`}
      >
        <div
          className={`absolute top-0 left-0 w-4 h-0.5 bg-primary/30 ${isActive ? "w-8" : "w-4"} transition-all duration-300 ease-out`}
        ></div>
        <div
          className={`absolute top-0 left-0 w-0.5 h-4 bg-primary/30 ${isActive ? "h-8" : "h-4"} transition-all duration-300 ease-out`}
        ></div>
      </div>

      <div
        className={`absolute bottom-0 right-0 w-16 h-16 ${isActive ? "opacity-100" : "opacity-0"} transition-opacity duration-300`}
      >
        <div
          className={`absolute bottom-0 right-0 w-4 h-0.5 bg-primary/30 ${isActive ? "w-8" : "w-4"} transition-all duration-300 ease-out`}
        ></div>
        <div
          className={`absolute bottom-0 right-0 w-0.5 h-4 bg-primary/30 ${isActive ? "h-8" : "h-4"} transition-all duration-300 ease-out`}
        ></div>
      </div>

      <CardHeader className="relative z-10 pb-2">
        <div className="flex justify-center mb-4 text-primary">
          <div
            className={`p-4 rounded-xl ${isActive ? "bg-primary/15" : "bg-primary/10"} border border-primary/20
            transform ${isActive ? "scale-110" : "scale-100"}
            transition-all duration-300 ease-out shadow-md`}
          >
            {icon}
          </div>
        </div>
        <CardTitle
          className={`text-center text-xl mb-2 ${isActive ? "text-primary" : ""} transition-colors duration-300`}
        >
          {title}
        </CardTitle>
        <CardDescription className="text-sm text-muted-foreground/80 transition-colors duration-300 line-clamp-2 px-2">
          {description}
        </CardDescription>
      </CardHeader>

      <CardFooter className="flex justify-center pt-2 pb-6 mt-auto relative z-10">
        <Button
          asChild
          variant={isActive ? "default" : "outline"}
          className="relative overflow-hidden transition-all duration-300"
          size="sm"
        >
          <Link href={id ? `/auth/login?project=${id}` : "/auth/login"} className="relative z-10">
            <span className="relative z-10 font-medium">{isActive ? "Select" : "View"}</span>
            <ArrowRight
              className={`ml-2 h-4 w-4 relative z-10 ${isActive ? "translate-x-1" : ""} transition-all duration-300`}
            />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
