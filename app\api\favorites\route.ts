import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

type FavoriteType = 'issue' | 'knowledge';

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('project_id');
    const type = searchParams.get('type') as FavoriteType || 'issue';

    if (!projectId) {
      return NextResponse.json(
        { error: "Project ID is required" },
        { status: 400 }
      );
    }

    // For knowledge base favorites, return empty array since we use localStorage only
    if (type === 'knowledge') {
      return NextResponse.json([]);
    }

    const token = await getToken({ req: request as any });
    if (!token?.accessToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Only handle issue favorites via backend API
    const endpoint = `${API_BASE_URL}/projects/${projectId}/favorites`;

    const response = await fetch(endpoint, {
      headers: {
        'Authorization': `Bearer ${token.accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);

      return NextResponse.json(
        { error: `Failed to fetch favorites: ${response.status} ${errorText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Favorites API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { itemId, type = 'issue' } = await request.json();

    if (!itemId) {
      return NextResponse.json(
        { error: "Item ID is required" },
        { status: 400 }
      );
    }

    // For knowledge base favorites, return success since we use localStorage only
    if (type === 'knowledge') {
      return new NextResponse(null, { status: 204 });
    }

    const token = await getToken({ req: request as any });
    if (!token?.accessToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Only handle issue favorites via backend API
    const endpoint = `${API_BASE_URL}/favorites/${itemId}`;

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token.accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);

      return NextResponse.json(
        { error: `Failed to add favorite: ${response.status} ${errorText}` },
        { status: response.status }
      );
    }

    return new NextResponse(null, { status: 204 });

  } catch (error) {
    console.error('Add favorite error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { itemId, type = 'issue' } = await request.json();

    if (!itemId) {
      return NextResponse.json(
        { error: "Item ID is required" },
        { status: 400 }
      );
    }

    // For knowledge base favorites, return success since we use localStorage only
    if (type === 'knowledge') {
      return new NextResponse(null, { status: 204 });
    }

    const token = await getToken({ req: request as any });
    if (!token?.accessToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Only handle issue favorites via backend API
    const endpoint = `${API_BASE_URL}/favorites/${itemId}`;

    const response = await fetch(endpoint, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token.accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);

      return NextResponse.json(
        { error: `Failed to remove favorite: ${response.status} ${errorText}` },
        { status: response.status }
      );
    }

    return new NextResponse(null, { status: 204 });

  } catch (error) {
    console.error('Remove favorite error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}