// Server Component
import type React from "react"
import { Inter } from "next/font/google"
import { AppSidebar } from "@/components/app-sidebar"
import "./globals.css"
import Providers from "@/components/providers"

const inter = Inter({ subsets: ["latin"] })

export default function RootLayout({
  children,}: { children: React.ReactNode}) 
  {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <div className="flex h-screen overflow-hidden">
            <AppSidebar/>
            <main className="flex-1 overflow-auto bg-background">
              {children}
            </main>
          </div>
        </Providers>
      </body>
    </html>
  )
}