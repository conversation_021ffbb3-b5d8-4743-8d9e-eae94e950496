"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, ChevronUp } from "lucide-react"
export function IssueFilters() {
  const [filters, setFilters] = useState({
    systems: {
      oms: false,
      wms: false,
      automation: false,
      other: false,
    },
    types: {
      database: false,
      api: false,
      ui: false,
      workflow: false,
      network: false,
      other: false,
    },
    impact: "all",
    dateRange: "all",
  })

  // State for collapsible sections
  const [openSections, setOpenSections] = useState({
    systems: true,
    types: true,
    impact: true,
    dateRange: true,
  })

  const toggleSection = (section: keyof typeof openSections) => {
    setOpenSections({
      ...openSections,
      [section]: !openSections[section],
    })
  }

  const handleSystemChange = (system: keyof typeof filters.systems) => {
    setFilters({
      ...filters,
      systems: {
        ...filters.systems,
        [system]: !filters.systems[system],
      },
    })
  }

  const handleTypeChange = (type: keyof typeof filters.types) => {
    setFilters({
      ...filters,
      types: {
        ...filters.types,
        [type]: !filters.types[type],
      },
    })
  }

  const handleImpactChange = (impact: string) => {
    setFilters({
      ...filters,
      impact,
    })
  }

  const handleDateRangeChange = (dateRange: string) => {
    setFilters({
      ...filters,
      dateRange,
    })
  }

  const resetFilters = () => {
    setFilters({
      systems: {
        oms: false,
        wms: false,
        automation: false,
        other: false,
      },
      types: {
        database: false,
        api: false,
        ui: false,
        workflow: false,
        network: false,
        other: false,
      },
      impact: "all",
      dateRange: "all",
    })
  }

  return (
    <div className="space-y-6">
      <Collapsible open={openSections.systems} onOpenChange={() => toggleSection("systems")}>
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">System/Module</h3>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
              {openSections.systems ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="pt-2">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="system-oms"
                checked={filters.systems.oms}
                onCheckedChange={() => handleSystemChange("oms")}
              />
              <Label htmlFor="system-oms">Order Management System</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="system-wms"
                checked={filters.systems.wms}
                onCheckedChange={() => handleSystemChange("wms")}
              />
              <Label htmlFor="system-wms">Warehouse Management System</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="system-automation"
                checked={filters.systems.automation}
                onCheckedChange={() => handleSystemChange("automation")}
              />
              <Label htmlFor="system-automation">Automation Platform</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="system-other"
                checked={filters.systems.other}
                onCheckedChange={() => handleSystemChange("other")}
              />
              <Label htmlFor="system-other">Other</Label>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      <Collapsible open={openSections.types} onOpenChange={() => toggleSection("types")}>
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Exception Type</h3>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
              {openSections.types ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="pt-2">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="type-database"
                checked={filters.types.database}
                onCheckedChange={() => handleTypeChange("database")}
              />
              <Label htmlFor="type-database">Database</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="type-api" checked={filters.types.api} onCheckedChange={() => handleTypeChange("api")} />
              <Label htmlFor="type-api">API</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="type-ui" checked={filters.types.ui} onCheckedChange={() => handleTypeChange("ui")} />
              <Label htmlFor="type-ui">UI</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="type-workflow"
                checked={filters.types.workflow}
                onCheckedChange={() => handleTypeChange("workflow")}
              />
              <Label htmlFor="type-workflow">Workflow</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="type-network"
                checked={filters.types.network}
                onCheckedChange={() => handleTypeChange("network")}
              />
              <Label htmlFor="type-network">Network</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="type-other"
                checked={filters.types.other}
                onCheckedChange={() => handleTypeChange("other")}
              />
              <Label htmlFor="type-other">Other</Label>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      <Collapsible open={openSections.impact} onOpenChange={() => toggleSection("impact")}>
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Impact Level</h3>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
              {openSections.impact ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="pt-2">
          <RadioGroup value={filters.impact} onValueChange={handleImpactChange} className="space-y-2">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="impact-all" />
              <Label htmlFor="impact-all">All</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="critical" id="impact-critical" />
              <Label htmlFor="impact-critical">Critical</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="high" id="impact-high" />
              <Label htmlFor="impact-high">High</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="medium" id="impact-medium" />
              <Label htmlFor="impact-medium">Medium</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="low" id="impact-low" />
              <Label htmlFor="impact-low">Low</Label>
            </div>
          </RadioGroup>
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      <Collapsible open={openSections.dateRange} onOpenChange={() => toggleSection("dateRange")}>
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Date Range</h3>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
              {openSections.dateRange ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="pt-2">
          <RadioGroup value={filters.dateRange} onValueChange={handleDateRangeChange} className="space-y-2">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="date-all" />
              <Label htmlFor="date-all">All Time</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="today" id="date-today" />
              <Label htmlFor="date-today">Today</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="week" id="date-week" />
              <Label htmlFor="date-week">This Week</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="month" id="date-month" />
              <Label htmlFor="date-month">This Month</Label>
            </div>
          </RadioGroup>
        </CollapsibleContent>
      </Collapsible>

      <Button variant="outline" size="sm" onClick={resetFilters} className="w-full">
        Reset Filters
      </Button>
    </div>
  )
}

