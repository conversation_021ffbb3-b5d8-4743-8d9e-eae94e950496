"use client"

import { useState } from "react"
import { SystemNameEnum } from "@/lib/api"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, CheckCircle, XCircle, Edit } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"

// Mock data for pending solutions
const mockPendingSolutions = [
  {
    id: 1,
    title: "Resolving database connection timeouts in OMS",
    system_name: SystemNameEnum.OMS,
    type: "Database",
    submittedBy: "<EMAIL>",
    submittedAt: "2023-03-15T10:30:00Z",
    solution:
      "Database connection timeouts in the Order Management System are often caused by connection pool exhaustion during peak hours. To resolve this issue:\n\n1. Increase the maximum connection pool size in the database configuration.\n2. Implement connection timeout handling and retry logic in the application code.\n3. Add proper connection release in finally blocks to ensure connections are returned to the pool.\n4. Monitor connection usage and implement connection pooling metrics.",
  },
  {
    id: 2,
    title: "Fixing API authentication failures in inventory sync",
    system_name: SystemNameEnum.WMS,
    type: "API",
    submittedBy: "<EMAIL>",
    submittedAt: "2023-03-14T15:45:00Z",
    solution:
      "API authentication failures during inventory synchronization are typically caused by expired credentials or incorrect authentication configuration. To resolve this issue:\n\n1. Verify that the API credentials are valid and have not expired.\n2. Check that the correct authentication method is being used (Basic Auth, OAuth, API Key, etc.).\n3. Ensure that the API client is properly handling authentication token refresh.\n4. Implement proper error handling for authentication failures with automatic retry logic.",
  },
  {
    id: 3,
    title: "Troubleshooting UI rendering errors in product catalog",
    system_name: SystemNameEnum.OMS,
    type: "UI",
    submittedBy: "<EMAIL>",
    submittedAt: "2023-03-13T09:15:00Z",
    solution:
      "UI rendering errors in the product catalog, particularly 'Cannot read property 'map' of undefined' errors, are typically caused by attempting to iterate over undefined data. To resolve this issue:\n\n1. Implement proper null/undefined checks before mapping over arrays.\n2. Use optional chaining and nullish coalescing operators for safer property access.\n3. Ensure that the API responses are properly handled and validated.\n4. Add error boundaries to prevent the entire UI from crashing.",
  },
  {
    id: 4,
    title: "Resolving stuck automation workflows",
    system_name: SystemNameEnum.AUTOMATION,
    type: "Workflow",
    submittedBy: "<EMAIL>",
    submittedAt: "2023-03-12T14:20:00Z",
    solution:
      "Automation workflows getting stuck in a pending state are often caused by unhandled exceptions or timeouts in workflow steps. To resolve this issue:\n\n1. Implement proper timeout handling for each workflow step.\n2. Add retry logic with exponential backoff for transient failures.\n3. Implement a dead-letter queue for workflows that cannot be processed after multiple retries.\n4. Add monitoring and alerting for stuck workflows.\n5. Implement a manual override mechanism for administrators to resolve stuck workflows.",
  },
]

export function SolutionValidation() {
  const [pendingSolutions, setPendingSolutions] = useState(mockPendingSolutions)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedSolution, setSelectedSolution] = useState<any>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editedSolution, setEditedSolution] = useState("")

  const filteredSolutions = pendingSolutions.filter(
    (solution) =>
      solution.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      solution.submittedBy.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleApproveSolution = (solutionId: number) => {
    setPendingSolutions(pendingSolutions.filter((solution) => solution.id !== solutionId))
    setIsDetailsDialogOpen(false)
  }

  const handleRejectSolution = (solutionId: number) => {
    setPendingSolutions(pendingSolutions.filter((solution) => solution.id !== solutionId))
    setIsDetailsDialogOpen(false)
  }

  const handleEditSolution = (solution: any) => {
    setSelectedSolution(solution)
    setEditedSolution(solution.solution)
    setIsEditDialogOpen(true)
  }

  const handleSaveEdit = () => {
    setPendingSolutions(
      pendingSolutions.map((solution) =>
        solution.id === selectedSolution?.id ? { ...solution, solution: editedSolution } : solution,
      ),
    )
    setIsEditDialogOpen(false)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search solutions..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">{pendingSolutions.length} pending solutions</span>
        </div>
      </div>

      <Tabs defaultValue="pending">
        <TabsList>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>
        <TabsContent value="pending" className="mt-4">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>System</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Submitted By</TableHead>
                  <TableHead>Submitted At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSolutions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                      No pending solutions found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredSolutions.map((solution) => (
                    <TableRow key={solution.id}>
                      <TableCell className="font-medium">{solution.title}</TableCell>
                      <TableCell>{solution.system_name}</TableCell>
                      <TableCell>{solution.type}</TableCell>
                      <TableCell>{solution.submittedBy}</TableCell>
                      <TableCell>{formatDate(solution.submittedAt)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Dialog
                            open={isDetailsDialogOpen && selectedSolution?.id === solution.id}
                            onOpenChange={(open) => {
                              setIsDetailsDialogOpen(open)
                              if (!open) setSelectedSolution(null)
                            }}
                          >
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedSolution(solution)
                                  setIsDetailsDialogOpen(true)
                                }}
                              >
                                Review
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-3xl">
                              <DialogHeader>
                                <DialogTitle>Review Solution</DialogTitle>
                                <DialogDescription>Review and approve or reject this solution.</DialogDescription>
                              </DialogHeader>
                              {selectedSolution && (
                                <div className="space-y-4 py-4">
                                  <div>
                                    <h3 className="text-lg font-medium">{selectedSolution.title}</h3>
                                    <div className="mt-2 flex flex-wrap gap-2">
                                      <Badge>{selectedSolution.system_name}</Badge>
                                      <Badge variant="outline">{selectedSolution.type}</Badge>
                                    </div>
                                  </div>
                                  <div>
                                    <h4 className="font-medium">Solution</h4>
                                    <div className="mt-2 rounded-md bg-muted p-4 whitespace-pre-line">
                                      {selectedSolution.solution}
                                    </div>
                                  </div>
                                  <div className="flex justify-between text-sm text-muted-foreground">
                                    <span>Submitted by: {selectedSolution.submittedBy}</span>
                                    <span>Submitted at: {formatDate(selectedSolution.submittedAt)}</span>
                                  </div>
                                </div>
                              )}
                              <DialogFooter>
                                <Button
                                  variant="outline"
                                  onClick={() => handleEditSolution(selectedSolution)}
                                  className="gap-1"
                                >
                                  <Edit className="h-4 w-4" /> Edit
                                </Button>
                                <Button
                                  variant="outline"
                                  onClick={() => handleRejectSolution(selectedSolution?.id)}
                                  className="gap-1"
                                >
                                  <XCircle className="h-4 w-4" /> Reject
                                </Button>
                                <Button onClick={() => handleApproveSolution(selectedSolution?.id)} className="gap-1">
                                  <CheckCircle className="h-4 w-4" /> Approve
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="approved" className="mt-4">
          <div className="text-center py-8">
            <p className="text-muted-foreground">Approved solutions will appear here.</p>
          </div>
        </TabsContent>
        <TabsContent value="rejected" className="mt-4">
          <div className="text-center py-8">
            <p className="text-muted-foreground">Rejected solutions will appear here.</p>
          </div>
        </TabsContent>
      </Tabs>

      <Dialog
        open={isEditDialogOpen}
        onOpenChange={(open) => {
          setIsEditDialogOpen(open)
          if (!open) setSelectedSolution(null)
        }}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Solution</DialogTitle>
            <DialogDescription>Make changes to the solution before approving.</DialogDescription>
          </DialogHeader>
          {selectedSolution && (
            <div className="space-y-4 py-4">
              <div>
                <h3 className="text-lg font-medium">{selectedSolution.title}</h3>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Solution</h4>
                <Textarea value={editedSolution} onChange={(e) => setEditedSolution(e.target.value)} rows={10} />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveEdit}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

