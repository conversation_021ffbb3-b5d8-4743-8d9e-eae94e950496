# Development Guide

## Table of Contents
1. [Development Environment Setup](#development-environment-setup)
2. [Project Structure](#project-structure)
3. [Coding Standards](#coding-standards)
4. [Git Workflow](#git-workflow)
5. [Testing](#testing)
6. [Debugging](#debugging)
7. [Code Review Process](#code-review-process)
8. [Documentation](#documentation)
9. [Performance Considerations](#performance-considerations)
10. [Security Best Practices](#security-best-practices)

## Development Environment Setup

### Prerequisites
- Node.js (v16+)
- Python (3.8+)
- PostgreSQL (v12+)
- Git
- Docker (optional)

### Local Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/extrack.git
   cd extrack
   ```

2. **Set up the frontend**
   ```bash
   # Install dependencies
   npm install
   
   # Create environment file
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Set up the backend**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: .\venv\Scripts\activate
   pip install -r requirements.txt
   
   # Create environment file
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   # Create and migrate database
   alembic upgrade head
   
   # Create initial admin user (if needed)
   python scripts/create_admin.py
   ```

5. **Start the development servers**
   ```bash
   # In the project root
   npm run dev
   ```

## Project Structure

```
extrack/
├── app/                    # Next.js app directory
│   ├── api/                # API routes
│   ├── components/         # Shared UI components
│   ├── contexts/           # React contexts
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Shared libraries
│   ├── pages/              # Page components
│   └── styles/             # Global styles
├── backend/                # FastAPI backend
│   ├── alembic/            # Database migrations
│   ├── app/                # Application code
│   │   ├── api/            # API endpoints
│   │   ├── core/           # Core functionality
│   │   ├── db/             # Database models
│   │   └── services/       # Business logic
│   └── tests/              # Backend tests
├── docs/                   # Documentation
├── public/                 # Static files
└── scripts/                # Utility scripts
```

## Coding Standards

### Frontend (TypeScript/React)
- Use functional components with TypeScript
- Follow the [React Hooks API](https://reactjs.org/docs/hooks-intro.html)
- Use ESLint and Prettier for code formatting
- Write unit tests with Jest and React Testing Library
- Use absolute imports (configured in tsconfig.json)

### Backend (Python/FastAPI)
- Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide
- Use type hints throughout the codebase
- Write docstrings for all public functions and classes
- Use Pydantic models for request/response validation
- Write unit tests with pytest

## Git Workflow

### Branch Naming
Format: `type/description`

Types:
- `feat/`: New features
- `fix/`: Bug fixes
- `docs/`: Documentation changes
- `style/`: Code style changes
- `refactor/`: Code changes that neither fix bugs nor add features
- `perf/`: Performance improvements
- `test/`: Adding or updating tests
- `chore/`: Maintenance tasks

### Commit Messages
Follow [Conventional Commits](https://www.conventionalcommits.org/):
```
type(scope): subject

[optional body]

[optional footer]
```

Example:
```
feat(auth): add password reset functionality

- Add password reset endpoint
- Create password reset email template
- Add tests for password reset flow

Closes #123
```

## Testing

### Frontend Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm test -- --watch

# Run specific test file
npm test -- path/to/test.tsx
```

### Backend Tests
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_auth.py

# Run with coverage
pytest --cov=app tests/
```

## Debugging

### Frontend
- Use React DevTools for component inspection
- Check browser console for errors
- Use `console.log` or `debugger` statements
- Check network tab for API request/response

### Backend
- Use VS Code debugger with launch configurations
- Check application logs
- Use `pdb` for Python debugging
- Check database queries with SQLAlchemy echo

## Code Review Process

1. Create a pull request from your feature branch to `main`
2. Request review from at least one team member
3. Address all review comments
4. Ensure all tests pass
5. Get approval from at least one reviewer
6. Squash and merge the PR

## Documentation

- Keep README.md up to date
- Document new API endpoints
- Add or update docstrings for new functions/classes
- Create/update architecture diagrams when making significant changes

## Performance Considerations

### Frontend
- Use React.memo for expensive components
- Implement code splitting
- Optimize images and assets
- Use React.lazy for route-based code splitting

### Backend
- Optimize database queries
- Use caching where appropriate
- Implement pagination for large datasets
- Monitor and optimize API response times

## Security Best Practices

### Frontend
- Sanitize all user inputs
- Use HTTPS in production
- Implement proper error handling
- Store sensitive data in environment variables

### Backend
- Validate all inputs
- Use parameterized queries
- Implement rate limiting
- Keep dependencies updated
- Use environment variables for sensitive data

## Deployment

### Staging
- Automatically deployed from `staging` branch
- Accessible at https://staging.extrack.example.com

### Production
- Deployed from `main` branch
- Requires manual approval
- Accessible at https://extrack.example.com

## Support

For development-related questions, contact the development <NAME_EMAIL>
