import { getSession } from 'next-auth/react';

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

/**
 * Refreshes the authentication token using the refresh token endpoint
 * @param useNextApiRoute Whether to use the Next.js API route or direct backend call
 * @returns The new access token or null if refresh failed
 */
export async function refreshToken(useNextApiRoute: boolean = false): Promise<string | null> {
  try {
    // Determine whether to use the Next.js API route or direct backend call
    const refreshUrl = useNextApiRoute 
      ? '/api/auth/refresh' // We would need to create this endpoint
      : `${API_BASE_URL}/refresh-token`;
      
    const response = await fetch(refreshUrl, {
      method: 'POST',
      credentials: 'include',
    });
    
    if (!response.ok) {
      throw new Error('Failed to refresh token');
    }
    
    const data = await response.json();
    
    // If we got a new token, update the session
    if (data.access_token) {
      // This is a client-side update that will be used until the session is refreshed
      const session = await getSession();
      if (session?.user) {
        session.user.accessToken = data.access_token;
      }
    }
    
    return data.access_token;
  } catch (error) {
    console.error('Token refresh failed:', error);
    return null;
  }
}

/**
 * Base API request function with authentication, token refresh, and error handling
 * @param endpoint API endpoint path (can be Next.js API route or backend endpoint)
 * @param options Fetch options
 * @returns Promise with the response data
 */
export async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const session = await getSession();
  
  // Determine if this is a call to a Next.js API route or direct backend call
  const isNextApiRoute = endpoint.startsWith('/api/');
  
  // For token endpoint, use form-urlencoded content type
  const isTokenEndpoint = endpoint.includes('/token');
  const isRefreshEndpoint = endpoint.includes('/refresh-token');
  
  const headers: HeadersInit = {
    'Content-Type': isTokenEndpoint ? 'application/x-www-form-urlencoded' : 'application/json',
    ...options.headers as Record<string, string>,
  };

  // Add authorization if we have a token and it's not an auth endpoint
  // For Next.js API routes, the API route itself will get the token from the session
  if (!isNextApiRoute && session?.user?.accessToken && !isTokenEndpoint && !isRefreshEndpoint) {
    headers['Authorization'] = `Bearer ${session.user.accessToken}`;
  }

  // Handle form data for token endpoint
  let modifiedBody = options.body;
  if (isTokenEndpoint && options.body && typeof options.body === 'string') {
    try {
      const jsonBody = JSON.parse(options.body);
      const formData = new URLSearchParams();
      Object.entries(jsonBody).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
      modifiedBody = formData;
    } catch (e) {
      console.error('Error parsing JSON body:', e);
    }
  }

  // Determine the full URL based on whether it's a Next.js API route or backend endpoint
  const fullUrl = isNextApiRoute 
    ? endpoint  // Next.js API routes are relative to the app
    : `${API_BASE_URL}${endpoint}`; // Backend calls need the base URL

  // Create a properly typed request options object
  const requestOptions: RequestInit = {
    ...options,
    headers,
    credentials: 'include',
  };
  
  // Only add body if it's not undefined
  if (modifiedBody !== undefined) {
    requestOptions.body = modifiedBody;
  }

  let response = await fetch(fullUrl, requestOptions);

  if (!isNextApiRoute && response.status === 401 && !isTokenEndpoint && !isRefreshEndpoint) {
    const newToken = await refreshToken(false); // Use direct backend call for refresh
    if (newToken) {
      // Update the authorization header with the new token
      headers['Authorization'] = `Bearer ${newToken}`;
      
      // Create a new request options object for the retry
      const retryRequestOptions: RequestInit = {
        ...options,
        headers,
        credentials: 'include',
      };
      
      // Only add body if it's not undefined
      if (modifiedBody !== undefined) {
        retryRequestOptions.body = modifiedBody;
      }
      
      // Retry the request with the new token
      response = await fetch(fullUrl, retryRequestOptions);
    } else {
      // Redirect to login if refresh failed
      window.location.href = `/auth/login?returnUrl=${encodeURIComponent(window.location.pathname + window.location.search)}`;
      return null as any;
    }
  }

  if (!response.ok) {
    // Handle error responses
    let errorMessage = 'API request failed';
    try {
      const error = await response.json();
      errorMessage = error.detail || error.error || errorMessage;
    } catch (e) {
      errorMessage = response.statusText || errorMessage;
    }
    throw new Error(errorMessage);
  }

  // For endpoints that might not return JSON
  if (response.headers.get('content-type')?.includes('application/json')) {
    return await response.json();
  }
  
  return { success: true } as any;
}

// User API functions
export const userApi = {
  /**
   * Get current user profile
   * Uses the Next.js API route which handles token refresh and fallback
   */
  getCurrentUser() {
    return apiRequest<any>('/api/users/me');
  },

  /**
   * Get all users (admin only)
   */
  getAllUsers() {
    return apiRequest<any[]>('/users');
  },

  /**
   * Get user by ID
   */
  getUser(userId: number) {
    return apiRequest<any>(`/users/${userId}`);
  },

  /**
   * Update user profile
   */
  updateUser(userId: number, userData: Partial<any>) {
    return apiRequest<any>(`/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }
};

// Project API functions
export const projectApi = {
  /**
   * Get all projects
   */
  getProjects() {
    return apiRequest<any[]>('/projects');
  },

  /**
   * Create a new project
   */
  createProject(data: { project_name: string; description: string; icon_name: string }) {
    return apiRequest<any>('/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
};

// Authentication functions
export const authApi = {
  /**
   * Login with email and password (direct API call, not using NextAuth)
   * This should only be used if NextAuth is not available
   */
  async login(email: string, password: string) {
    return apiRequest<any>('/token', {
      method: 'POST',
      body: JSON.stringify({
        username: email,
        password: password,
      }),
    });
  },

  /**
   * Register a new user
   */
  async register(userData: { name: string; email: string; password: string; role?: string; project_id?: string | null }) {
    return apiRequest<any>('/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }
};