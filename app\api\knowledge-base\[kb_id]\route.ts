import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

export async function GET(
  request: NextRequest,
  { params }: { params: { kb_id: string } }
) {
  const { kb_id: id } = params;
  try {
    // Check if id is a valid integer
    const kbIdNum = parseInt(id, 10);
    if (isNaN(kbIdNum) || kbIdNum <= 0) {
      return NextResponse.json(
        { error: 'Invalid knowledge base ID. Must be a positive integer.' },
        { status: 400 }
      );
    }
    
    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Make the request to the backend API
    if (!id) {
      return NextResponse.json(
        { error: 'Knowledge base ID is required' },
        { status: 400 }
      );
    }

    const response = await fetch(`${API_BASE_URL}/knowledge-base/${encodeURIComponent(id)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token['accessToken']}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000) // 10 second timeout
    }).catch(error => {
      if (error.name === 'TimeoutError') {
        throw new Error('Request to backend API timed out');
      }
      throw error;
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);
      
      return NextResponse.json(
        { error: `Failed to fetch knowledge entry: ${response.status} ${errorText}` },
        { status: response.status }
      );
    }

    // Parse and return the response from the backend
    const data = await response.json().catch(() => ({}));
    
    // Ensure we're returning a proper response even if JSON parsing fails
    if (!response.ok) {
      return NextResponse.json(
        { 
          error: data.error || 'Failed to fetch knowledge entry',
          details: data.details
        },
        { status: response.status }
      );
    }
    
    return NextResponse.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Knowledge base API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}