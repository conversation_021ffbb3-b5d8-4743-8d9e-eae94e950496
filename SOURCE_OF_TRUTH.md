# Extrack – Project Source of Truth

## 1. Project Overview

**Extrack** is a comprehensive exception tracking and knowledge management system for development teams. It provides a centralized platform for:
- Logging and tracking software exceptions/issues
- Maintaining a searchable knowledge base
- Documenting and sharing solutions
- Organizing by project and user roles
- Powerful search and RESTful API access

---

## 2. Tech Stack

### Frontend
- **Framework**: Next.js (React, TypeScript)
- **UI**: Radix UI Primitives, Tailwind CSS
- **State Management**: React Context API, SWR
- **Form Handling**: React Hook Form + Zod
- **Build Tools**: npm, Turbopack, PostCSS

### Backend
- **Framework**: FastAPI (Python 3.8+)
- **ORM**: SQLAlchemy
- **Database** :MySQL 
- **Authentication**: JWT (JSON Web Tokens), OAuth2
- **API Docs**: OpenAPI (Swagger UI)
- **Migrations**: Alembic

### DevOps & Tooling
- **Containerization**: Docker, Compose
- **CI/CD**: Pipeline ready (configurable)
- **Version Control**: Git
- **Testing**: Pytest, TypeScript types

---

## 3. Functional Requirements (Key Highlights)

(See `/docs/requirements/functional_requirements.md` for full details)

- **User Management**: Registration, JWT login, RBAC (Admin, Manager, Developer, Viewer), password reset, token refresh
- **Project Management**: CRUD, unique names, team assignment, permissions
- **Issue Tracking**: Create, update, assign, comment, attach files, status workflow (Open → Closed), full-text search, filtering
- **Knowledge Base**: Article CRUD, rich text, categorization, versioning, linking to issues, search, related suggestions
- **Solution Management**: Propose/verify/vote on solutions, track effectiveness, convert to KB articles
- **Reporting & Analytics**: Dashboard metrics, activity feed, reports export (CSV/PDF)
- **Notifications**: In-app and email, configurable, digest support
- **API & Integrations**: RESTful API, OAuth2, webhooks, SDKs (JS/TS, Python, Java, C#)
- **Administration**: System config, custom fields, audit logs

---

## 4. Non-Functional Requirements (Key Highlights)

(See `/docs/requirements/non_functional_requirements.md` for full details)

- **Performance**: API <500ms (95th percentile), initial page <2s, search <1s for 1M records
- **Scalability**: Horizontal/vertical scaling, DB sharding, read replicas
- **Availability**: 99.9% uptime, status page, fault tolerance
- **Security**: Bcrypt password hashing, JWT expiry, TLS, GDPR compliance, audit logs
- **Usability**: Responsive UI, accessibility (WCAG 2.1 AA), tooltips, guides
- **Maintainability**: 80%+ test coverage, code style guides, comprehensive logging, CI/CD, blue-green deployments
- **Data**: ACID, indexing, daily backups, point-in-time recovery
- **Internationalization**: Multi-language, RTL support, localization
- **Monitoring**: Health checks, Prometheus, centralized logging

---

## 5. System Architecture

- **Modern decoupled architecture**: Clear separation between frontend (Next.js) and backend (FastAPI)
- **API-first**: RESTful, JSON:API, OpenAPI docs, rate limiting, versioning, pagination, filtering, field selection, webhooks
- **Authentication & Authorization**: JWT, RBAC, secure password hashing, token refresh
- **Deployment**: Dockerized, deployable to Kubernetes, cloud, or VPS

---

## 6. Database Model (see `/backend/DB/structure_v1.sql`)

- **Users**: Roles, authentication, profile
- **Admins**: Linked to users
- **Projects**: Name, description, icon, timestamps
- **Project Access**: User-project-role mapping
- **Issues**: Project, category, title, description, status, workflow, error details, reviewer, timestamps
- **Solutions**: Linked to issues, voting, verification, effectiveness
- **Solution History**: Versioning for solutions
- **Issue Files**: Attachments
- **Knowledge Base**: Articles, linking to projects/issues/solutions, file attachments

---

## 7. Core Functionalities

- **Full-featured issue tracker** with workflow, assignments, comments, file uploads
- **Knowledge base** with versioning, search, linking to issues/solutions
- **Solution management** with voting, verification, conversion to KB
- **Role-based access control** for all entities
- **Powerful search and analytics** across issues and KB
- **RESTful API** for all entities, with SDKs and webhooks
- **Notifications** (in-app/email), customizable
- **Admin dashboard** for system config, audit logs, and user management

---

## 8. Future Plans & Recommendations

- **AI-powered suggestions** for solutions and KB articles
- **Advanced analytics**: Team performance, issue trends, predictive insights
- **Mobile app**: Native or PWA
- **Marketplace for community-shared solutions**
- **Plugin system** for integrations (Jira, Slack, Teams, etc.)
- **Custom workflow builder** for issue states
- **Self-service onboarding and help center**
- **Enhanced internationalization** (more languages, region-specific compliance)
- **Cloud-native scaling**: Managed DB, serverless, auto-scaling

---

## 9. Reference

- For detailed requirements: `/docs/requirements/functional_requirements.md` and `/docs/requirements/non_functional_requirements.md`
- For architecture: `/docs/architecture/overview.md`
- For API: `/docs/api/README.md`
- For DB schema: `/backend/DB/structure_v1.sql`
- For setup and usage: `README.md` in project root

---

This document is a living source of truth and should be updated as your codebase and requirements evolve.
