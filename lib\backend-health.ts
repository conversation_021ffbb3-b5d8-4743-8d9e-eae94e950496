/**
 * Backend health check utilities
 */

const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export interface HealthCheckResult {
  isHealthy: boolean;
  error?: string;
  responseTime?: number;
}

/**
 * Check if the backend server is running and healthy
 */
export async function checkBackendHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${BACKEND_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Set a reasonable timeout
      signal: AbortSignal.timeout(5000),
    });

    const responseTime = Date.now() - startTime;

    if (response.ok) {
      return {
        isHealthy: true,
        responseTime,
      };
    } else {
      return {
        isHealthy: false,
        error: `Backend returned status ${response.status}`,
        responseTime,
      };
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    if (error instanceof Error) {
      if (error.name === 'TimeoutError') {
        return {
          isHealthy: false,
          error: 'Backend server is not responding (timeout)',
          responseTime,
        };
      } else if (error.message.includes('fetch')) {
        return {
          isHealthy: false,
          error: 'Cannot connect to backend server. Please ensure it is running on port 8000.',
          responseTime,
        };
      }
    }

    return {
      isHealthy: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      responseTime,
    };
  }
}

/**
 * Check backend health and show user-friendly error messages
 */
export async function checkBackendWithUserFeedback(): Promise<{
  isHealthy: boolean;
  userMessage?: string;
}> {
  const result = await checkBackendHealth();
  
  if (result.isHealthy) {
    return { isHealthy: true };
  }

  let userMessage = 'Unable to connect to the backend server. ';
  
  if (result.error?.includes('timeout')) {
    userMessage += 'The server is taking too long to respond. Please try again later.';
  } else if (result.error?.includes('port 8000')) {
    userMessage += 'Please ensure the backend server is running by executing "python run.py" in your terminal.';
  } else {
    userMessage += 'Please check your internet connection and try again.';
  }

  return {
    isHealthy: false,
    userMessage,
  };
}
