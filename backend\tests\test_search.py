import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from backend.models import Issue
from backend.main import app
def test_search_issues(test_db: Session, client: TestClient):
    # Setup test data
    test_issues = [
        Issue(
            project_id=1,
            title="Test Issue 1",
            error_message="Error 1",
            description="Description 1",
            category="BUG",
            status="OPEN"
        ),
        Issue(
            project_id=1,
            title="Another Issue",
            error_message="Error 2",
            description="Description 2",
            category="BUG",
            status="OPEN"
        ),
        Issue(
            project_id=2,  # Different project
            title="Test Issue 3",
            error_message="Error 3",
            description="Description 3",
            category="BUG",
            status="OPEN"
        )
    ]
    
    for issue in test_issues:
        test_db.add(issue)
    test_db.commit()

    # Test cases
    test_cases = [
        {
            "name": "basic search with query",
            "params": {"project_id": 1, "query": "Test"},
            "expected_count": 1,
            "expected_title": "Test Issue 1"
        },
        {
            "name": "pagination test",
            "params": {"project_id": 1, "page": 1, "per_page": 1},
            "expected_count": 1
        },
        {
            "name": "all issues for project",
            "params": {"project_id": 1},
            "expected_count": 2
        },
        {
            "name": "no results",
            "params": {"project_id": 1, "query": "nonexistent"},
            "expected_count": 0
        }
    ]

    # Run tests
for case in test_cases:
        response = client.get(f"/projects/{case['params']['project_id']}/issues", params=case['params'])
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total"] == case["expected_count"]
        if "expected_title" in case:
            assert len(data["items"]) > 0
            assert data["items"][0]["title"] == case["expected_title"]

def test_search_issues_invalid_params(client: TestClient):
    # Test invalid page number
    response = client.get("/projects/1/issues", params={"page": 0})
    assert response.status_code == 422

    # Test invalid per_page number
    response = client.get("/projects/1/issues", params={"per_page": 101})
    assert response.status_code == 422

    # Test invalid project_id
    response = client.get("/projects/invalid/issues")
    assert response.status_code == 422