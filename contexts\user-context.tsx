"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useEffect, useMemo, useState } from "react"
import { useSession } from "next-auth/react"
import { Session } from "next-auth"

// Extend the Session type to include is_admin property
interface ExtendedSession extends Session {
  user: {
    is_admin?: boolean
  } & Session["user"]
}

interface User {
  id: string
  name: string
  email: string
  is_admin: boolean
  user_id?: number
  created_at?: string
  // Add other fields returned by your backend
}

interface UserContextType {
  user: User | null
  loading: boolean
  error: string | null
  is_admin: boolean
  refreshUser: () => Promise<void>
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  // Get session from next-auth
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { data: session, status } = useSession() as { data: ExtendedSession | null, status: "loading" | "authenticated" | "unauthenticated" }

  const fetchUser = async () => {
    try {
      // If not authenticated, don't try to fetch user data
      if (status === 'unauthenticated') {
        setUser(null)
        setError('Not authenticated')
        setLoading(false)
        return
      }

      // If still loading session, wait
      if (status === 'loading') {
        return
      }

      // Import the userApi from our consolidated API utility
      const { userApi } = await import('@/utils/auth-api');
      
      try {
        // Use the getCurrentUser function from our API utility
        const data = await userApi.getCurrentUser();
        console.log('User data from API:', data);
        
        // Check if we received a new access token from the API
        if (data.new_access_token) {
          console.log('Received new access token from API');
          // Update the session with the new token
          // This is a client-side update that will be used until the session is refreshed
          if (session?.user) {
            session.user.accessToken = data.new_access_token;
          }
        }
        
        // Map the backend response to the frontend user object
        const mappedUser = {
          id: data.user_id?.toString() || data.id,
          name: data.name,
          email: data.email,
          is_admin: data.is_admin === true,
          user_id: data.user_id,
          created_at: data.created_at
        };
        
        console.log('Mapped user data:', mappedUser);
        setUser(mappedUser);
        setError(null);
      } catch (apiError) {
        // If the API call fails, try to use the session data as a fallback
        if (session?.user) {
          console.log('Using session data as fallback:', session.user);
          const sessionUser = {
            id: session.user.id,
            name: session.user.name || 'User',
            email: session.user.email || '',
            is_admin: session.user.is_admin === true,
          };
          setUser(sessionUser);
          setError(null);
        } else {
          throw apiError;
        }
      }
    } catch (err) {
      console.error("Error fetching user:", err)
      setError((err as Error).message || "An unknown error occurred")
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const refreshUser = async () => {
    setLoading(true)
    await fetchUser()
  }

  useEffect(() => {
    if (status === 'authenticated') {
      fetchUser()
    }
  }, [status])

  // Check if user is admin from both user data and session
  const isAdmin = useMemo(() => {
    // Only check admin status if we have a user or session
    if (!user && !session) {
      return false;
    }
    
    // Check user data from API
    const userIsAdmin = user?.is_admin === true
    // Check session data - make sure to check both possible locations
    const sessionIsAdmin = session?.user?.is_admin === true || session?.user?.role === 'admin'
    
    // Only log when we have meaningful data to check
    if (user || session) {
      console.log('Admin status check:', { 
        userIsAdmin, 
        sessionIsAdmin, 
        'user?.is_admin': user?.is_admin,
        'session?.user?.is_admin': session?.user?.is_admin,
        'session?.user?.role': session?.user?.role
      })
    }
    
    // Return true if either source indicates admin status
    return userIsAdmin || sessionIsAdmin
  }, [user, session])
  
  // Update context value
  const contextValue = useMemo(
    () => {
      const contextIsAdmin = status === 'authenticated' ? isAdmin : false;
      
      return { 
        user, 
        is_admin: contextIsAdmin,
        loading: loading || status === 'loading', 
        error: status === 'unauthenticated' ? 'Not authenticated' : error, 
        refreshUser 
      };
    },
    [user, isAdmin, loading, error, status]
  );

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider")
  }
  return context
}
