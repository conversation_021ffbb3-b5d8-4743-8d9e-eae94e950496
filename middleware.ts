import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { securityHeaders } from './lib/security-headers';

/**
 * Middleware function that adds security headers and handles authentication
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Get the response
  const response = NextResponse.next();

  // Add security headers to the response
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Skip authentication check for public routes
  if (pathname === '/' || pathname.startsWith('/auth/') || pathname.startsWith('/api/auth/')) {
    return response;
  }

  // Check authentication for protected routes
  const token = await getToken({ req: request });

  if (!token) {
    // Redirect to login with return URL
    const loginUrl = new URL('/auth/login', request.url);
    const project = request.nextUrl.searchParams.get('project');
    if (project) {
      loginUrl.searchParams.set('project', project);
    }
    loginUrl.searchParams.set('returnUrl', pathname + request.nextUrl.search);
    return NextResponse.redirect(loginUrl);
  }

  return response;
}

// Apply middleware to routes based on the configuration
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - robots.txt (robots file)
     * - sitemap.xml (sitemap file)
     */
    {
      source: '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
      missing: [
        { type: 'header', key: 'next-router-prefetch' },
        { type: 'header', key: 'purpose', value: 'prefetch' },
      ],
    },
  ],
};
