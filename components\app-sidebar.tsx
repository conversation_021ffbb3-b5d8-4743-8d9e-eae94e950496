"use client"

import { Suspense } from "react"
import Link from "next/link"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import { Layers, LogOut, Menu, X } from "lucide-react"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

function getInitials(name?: string) {
  if (!name) return "?"
  const words = name.trim().split(" ")
  return words.length > 1 && words[0]?.[0] && words[1]?.[0]
    ? `${words[0][0]}${words[1][0]}`
    : words[0]?.[0] || "?"
}

import { Button } from "@/components/ui/button"
import { Sheet, SheetContent } from "@/components/ui/sheet"
import { useSidebar } from "./sidebar-provider"
import { useUser } from "@/contexts/user-context";
import { signOut } from "next-auth/react";
import { DropdownMenu,DropdownMenuContent,DropdownMenuItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import { useAppNavigation } from "@/hooks/useAppNavigation"
import { cn } from "@/lib/utils"
import { useMemo } from "react"

export function AppSidebar() {
  return (
    <Suspense fallback={<LoadingSpinner className="h-screen w-16 flex items-center justify-center" />}>
      <AppSidebarContent />
    </Suspense>
  )
}

function AppSidebarContent() {
  const pathname = usePathname()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { open, mobileOpen, setMobileOpen, isMobile, setOpen } = useSidebar()
  const { user, loading, error } = useUser()
  
  // Get project ID from URL parameters
  const projectId = searchParams.get('project')
  
  // Get base navigation from the hook (moved before any conditional returns)
  const baseNavigation = useAppNavigation(projectId)
  
  // Navigation items with additional sidebar-specific items
  const navigation = useMemo(() => {
    return [...baseNavigation];
  }, [baseNavigation])
  
  // Early return for auth pages (moved after all hooks)
  if (pathname === "/" || pathname.startsWith("/auth")) {
    return <div className="hidden" />
  }

  const isActive = (path: string) => {
    // Extract the base path and query params from the navigation item's href
    const [basePath, queryString] = path.split('?')
    const [currentPath, currentQuery] = pathname.split('?')
    
    // Check if the base paths match exactly
    const pathMatch = currentPath === basePath
    
    // If there are query parameters, check if they match
    if (queryString && currentQuery) {
      const navParams = new URLSearchParams(queryString)
      const currentParams = new URLSearchParams(currentQuery)
      return pathMatch && navParams.get('project') === currentParams.get('project')
    }
    
    // If no query parameters, just check the base path
    return pathMatch
  }

  const sidebarContent = (
    <div className="flex h-full flex-col overflow-hidden">
      <div className="flex-shrink-0 border-b">
        <div className="flex h-16 items-center px-3 py-3">
          <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
            <Layers className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">ExTrack</span>
          </Link>
          {isMobile && (
            <Button variant="ghost" size="icon" className="ml-auto" onClick={() => setMobileOpen(false)}>
              <X className="h-5 w-5" />
            </Button>
          )}
        </div>
      </div>
      <div className="flex-1 overflow-y-auto overflow-x-hidden py-4 px-1">
        <nav className="grid gap-2">
          {navigation.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              onClick={() => isMobile && setMobileOpen(false)}
              className={cn(
                "flex items-center gap-3 rounded-md px-4 py-2.5 text-sm font-medium hover:bg-accent",
                isActive(item.href) ? "bg-accent text-accent-foreground" : "text-muted-foreground",
              )}
            >
              <item.icon className="h-5 w-5 flex-shrink-0" />
              <span className="truncate">{item.name}</span>
            </Link>
          ))}
        </nav>
      </div>
      <div className="flex-shrink-0 border-t bg-muted/30 relative z-10">
        <div className="flex items-center justify-between p-4 hover:bg-accent transition-colors relative">
          {loading ? (
            <div className="h-10 w-10 rounded-full bg-primary/10 animate-pulse flex-shrink-0" />
          ) : error ? (
            <div className="flex items-center gap-2 text-sm text-destructive w-full">
              <span className="font-medium">Error:</span> <span className="truncate">{error}</span>
            </div>
          ) : (
            <>
              <div className="flex items-center min-w-0 flex-1">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                  <span className="text-sm font-medium text-primary">{getInitials(user?.name)}</span>
                </div>
                <div className="flex flex-col ml-3 min-w-0 flex-1">
                  <p className="text-sm font-medium truncate">{user?.name || 'User'}</p>
                  <p className="text-xs text-muted-foreground truncate">{user?.email || ''}</p>
                  {user?.is_admin && (
                    <span className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full mt-1 inline-block self-start">
                      Admin
                    </span>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-1 ml-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 p-1.5 text-muted-foreground hover:text-foreground hover:bg-transparent"
                      aria-label="User menu"
                    >
                      <Menu className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="z-50">
                    <DropdownMenuLabel>Account</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={async () => {
                        await signOut({ redirect: false });
                        router.push('/');
                      }}
                      className="text-destructive focus:text-destructive cursor-pointer"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Logout
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </>
          )}
        </div>
      </div>
      

    </div>
  )

  if (isMobile) {
    return (
      <>
        <Button
          variant="outline"
          size="icon"
          className="fixed left-4 top-4 z-20 lg:hidden"
          onClick={() => setMobileOpen(!mobileOpen)}
        >
          <Menu className="h-4 w-4" />
        </Button>
        <Sheet open={mobileOpen} onOpenChange={setMobileOpen}>
          <SheetContent side="left" className="p-0 w-[280px]" title="Navigation">
            {sidebarContent}
          </SheetContent>
        </Sheet>
      </>
    )
  }

  return (
    <>
      <div
        className={cn(
          "hidden md:flex h-screen border-r bg-background transition-all duration-300 ease-in-out overflow-hidden",
          open ? "w-64" : "w-0"
        )}
      >
        {open && sidebarContent}
      </div>
      <Button
        variant="ghost"
        size="icon"
        className={cn(
          "fixed z-40 p-2 top-4",
          open ? "left-[250px]" : "left-2"
        )}
        onClick={() => setOpen(!open)}
      >
        {open ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>
    </>
  )
}
