# API Documentation

## Base URL
```
https://api.yourdomain.com/v1
```

## Authentication

Most API endpoints require authentication using JWT (JSON Web Tokens). Include the token in the `Authorization` header:

```
Authorization: Bearer your_jwt_token_here
```

### Authentication Endpoints

#### Login

```http
POST /auth/login
```

**Request Body:**
```json
{
  "username": "<EMAIL>",
  "password": "yourpassword"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "full_name": "<PERSON>",
    "role": "admin"
  }
}
```

#### Refresh Token

```http
POST /auth/refresh
```

**Headers:**
```
Authorization: Bearer your_refresh_token_here
```

## Core Resources

### Projects

#### List Projects

```http
GET /projects
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "Web Application",
    "description": "Main customer-facing web application",
    "created_at": "2023-01-15T10:30:00Z"
  },
  {
    "id": 2,
    "name": "Mobile App",
    "description": "iOS and Android mobile application",
    "created_at": "2023-02-20T14:15:00Z"
  }
]
```

### Issues

#### Create Issue

```http
POST /issues
```

**Request Body:**
```json
{
  "project_id": 1,
  "title": "Login page not loading",
  "description": "Users report 500 error when accessing login page",
  "category": "bug",
  "priority": "high"
}
```

**Response:**
```json
{
  "id": 123,
  "project_id": 1,
  "title": "Login page not loading",
  "description": "Users report 500 error when accessing login page",
  "status": "open",
  "category": "bug",
  "priority": "high",
  "created_at": "2023-06-15T09:45:00Z",
  "updated_at": "2023-06-15T09:45:00Z"
}
```

### Knowledge Base

#### Search Knowledge Base

```http
GET /knowledge?q=login+error
```

**Response:**
```json
{
  "results": [
    {
      "id": 1,
      "title": "Common Login Issues and Solutions",
      "content": "This article covers common login issues...",
      "category": "authentication",
      "created_at": "2023-05-10T11:20:00Z"
    }
  ],
  "total": 1
}
```

## Error Handling

### Error Responses

All error responses follow this format:

```json
{
  "detail": "Error message describing the issue",
  "status": 400,
  "code": "error_code"
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid request parameters |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 422 | Unprocessable Entity - Validation error |
| 500 | Internal Server Error - Something went wrong |

## Rate Limiting

- 1000 requests per hour per IP address
- 100 requests per minute per authenticated user

## Versioning

API versioning is done through the URL path:
```
/api/v1/resource
```

## Pagination

List endpoints support pagination using `limit` and `offset` query parameters:

```
GET /issues?limit=10&offset=20
```

## Sorting

Most list endpoints support sorting using the `sort` parameter:

```
GET /issues?sort=-created_at,priority
```

## Filtering

Filter resources using query parameters:

```
GET /issues?status=open&priority=high&project_id=1
```

## Field Selection

Request specific fields using the `fields` parameter:

```
GET /issues/123?fields=id,title,status,created_at
```

## Related Resources

Include related resources using the `include` parameter:

```
GET /issues/123?include=project,comments,assignee
```

## Webhooks

### Available Webhooks

- `issue.created` - Triggered when a new issue is created
- `issue.updated` - Triggered when an issue is updated
- `comment.created` - Triggered when a new comment is added

### Webhook Payload

```json
{
  "event": "issue.created",
  "data": {
    "id": 123,
    "title": "Login page not loading",
    "status": "open",
    "project_id": 1,
    "created_at": "2023-06-15T09:45:00Z"
  },
  "timestamp": "2023-06-15T09:45:01Z"
}
```

## SDKs

Official SDKs are available for:

- JavaScript/TypeScript
- Python
- Java
- C#

## Support

For API support, <NAME_EMAIL>
