import "next-auth"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Default<PERSON><PERSON> } from "next-auth"
import { JWT as DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name: string | null
      email: string | null
      image: string | null
      accessToken: string
      refreshToken: string
      role: string
      is_admin: boolean
    } & DefaultSession["user"]
  }

  interface User extends DefaultUser {
    id: string
    name: string | null
    email: string | null
    image: string | null
    accessToken: string
    refreshToken: string
    role: string
    is_admin: boolean
  }
}

declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    id: string
    name: string | null
    email: string | null
    accessToken: string
    refreshToken: string
    role: string
    is_admin: boolean
  }
}
