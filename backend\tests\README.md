# Running Tests

To run the tests in this directory, make sure you're in the root directory of the project (`extrack`) and run:

```bash
python -m pytest backend/tests
```

This ensures that the Python module resolution works correctly for the imports in the test files.

## Individual Test Files

To run a specific test file:

```bash
python -m pytest backend/tests/test_search.py
```

## Test Coverage

To run tests with coverage:

```bash
python -m pytest --cov=backend backend/tests
```

Note: You may need to install pytest-cov for coverage reporting:

```bash
pip install pytest-cov
```