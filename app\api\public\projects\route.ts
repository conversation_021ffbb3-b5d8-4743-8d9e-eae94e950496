import { NextRequest, NextResponse } from "next/server";

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

/**
 * Public API endpoint for getting projects
 * This endpoint doesn't require authentication and is used on the homepage
 */
export async function GET(request: NextRequest) {
  try {
    const apiUrl = `${API_BASE_URL}/public/projects`;
    console.log(`Attempting to fetch public projects from: ${apiUrl}`);
    
    // Make the request to the backend API
    // Note: This is a special case where we're not adding authentication
    // This is only for the homepage to show available projects before login
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      cache: 'no-store'
    }).catch(error => {
      console.error('Fetch error for public projects:', error);
      throw new Error(`Failed to connect to backend at ${apiUrl}. Is the backend server running?`);
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        { error: errorData.detail || `Failed to fetch projects: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching public projects:", error);
    return NextResponse.json(
      { error: "Failed to fetch projects" },
      { status: 500 }
    );
  }
}
