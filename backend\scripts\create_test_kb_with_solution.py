#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a test knowledge base entry with a connected solution.
This will help test the Solution tab functionality in the Knowledge Base Details Dialog.
"""

import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from backend.database import SessionLocal
from backend.models import KnowledgeBase, User, Project, Solution, Issue, CategoryEnum, SystemNameEnum
from datetime import datetime

def create_test_data():
    """Create test data including an issue, solution, and knowledge base entry"""
    db = SessionLocal()
    try:
        # Check if we have any users
        user = db.query(User).first()
        if not user:
            print("No users found in the database. Please create a user first.")
            return
        
        # Check if we have any projects
        project = db.query(Project).first()
        if not project:
            print("No projects found in the database. Please create a project first.")
            return
        
        # Create a test issue first (needed for the solution)
        test_issue = Issue(
            project_id=project.project_id,
            category=CategoryEnum.EXCEPTIONS,
            title="Sample Database Connection Issue",
            error_code="DB_CONN_001",
            error_message="Connection timeout to database server",
            system_name=SystemNameEnum.OMS,
            issue_type="Database",
            status="Resolved",
            impact="High",
            frequency="Frequent",
            description="Users experiencing timeout errors when connecting to the main database server during peak hours.",
            created_by=user.user_id
        )
        
        db.add(test_issue)
        db.commit()
        db.refresh(test_issue)
        print(f"Created test issue with ID: {test_issue.issue_id}")
        
        # Create a solution for the issue
        test_solution = Solution(
            issue_id=test_issue.issue_id,
            category=CategoryEnum.EXCEPTIONS,
            provided_by=user.user_id,
            solution_text="""To resolve the database connection timeout issue:

1. **Increase Connection Pool Size**: 
   - Update the database connection pool configuration
   - Set max_connections to 200 (from 100)
   - Set pool_timeout to 30 seconds

2. **Optimize Database Queries**:
   - Review and optimize slow-running queries
   - Add appropriate indexes for frequently accessed tables
   - Implement query caching for read-heavy operations

3. **Load Balancing**:
   - Configure read replicas for SELECT operations
   - Route write operations to the primary database
   - Implement connection retry logic with exponential backoff

4. **Monitoring**:
   - Set up database performance monitoring
   - Configure alerts for connection pool exhaustion
   - Monitor query execution times

This solution has been tested in our staging environment and reduced connection timeouts by 95%.""",
            upvotes=5
        )
        
        db.add(test_solution)
        db.commit()
        db.refresh(test_solution)
        print(f"Created test solution with ID: {test_solution.solution_id}")
        
        # Create a knowledge base entry linked to the solution
        test_kb_entry = KnowledgeBase(
            project_id=project.project_id,
            title="Database Connection Timeout - Best Practices",
            content="""# Database Connection Timeout Resolution Guide

## Problem Description
This knowledge base entry documents the resolution process for database connection timeout issues that commonly occur during peak usage hours.

## Symptoms
- Users report "Connection timeout" errors
- Application becomes unresponsive during database operations
- Increased error rates in application logs
- Database connection pool exhaustion

## Root Causes
1. Insufficient connection pool configuration
2. Unoptimized database queries
3. Lack of proper load balancing
4. Missing monitoring and alerting

## Impact
- **Severity**: High
- **Frequency**: Occurs during peak hours (9 AM - 5 PM)
- **Affected Systems**: OMS, WMS, Customer Portal
- **Business Impact**: Customer complaints, order processing delays

## Related Documentation
- Database Configuration Guide
- Performance Optimization Checklist
- Monitoring Setup Instructions

## Tags
#database #performance #timeout #connection-pool #optimization""",
            solution_id=test_solution.solution_id,
            uploaded_by=user.user_id
        )
        
        db.add(test_kb_entry)
        db.commit()
        db.refresh(test_kb_entry)
        
        print(f"Successfully created knowledge base entry with ID: {test_kb_entry.kb_id}")
        print(f"Knowledge base entry is linked to solution ID: {test_solution.solution_id}")
        print("\nTest data created successfully!")
        print("You can now test the Solution tab in the Knowledge Base Details Dialog.")
        
    except Exception as e:
        db.rollback()
        print(f"Error creating test data: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_data()
