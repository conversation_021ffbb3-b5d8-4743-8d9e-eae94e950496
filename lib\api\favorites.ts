import { api } from './client';
import { Favorite, Issue, PaginatedResponse } from './types';

const FAVORITES_BASE = '/favorites';

export const favoritesApi = {
  /**
   * Get user's favorite issues
   */
  getUserFavorites: (params?: { page?: number; limit?: number }) =>
    api.get<PaginatedResponse<Favorite>>(FAVORITES_BASE, { params: params as Record<string, any> }),

  /**
   * Add issue to favorites
   */
  addFavorite: (issueId: number) =>
    api.post<Favorite>(FAVORITES_BASE, { issue_id: issueId }),

  /**
   * Remove issue from favorites
   */
  removeFavorite: async (favoriteId: number) => {
    try {
      await api.delete<{ success: boolean }>(`${FAVORITES_BASE}/${favoriteId}`, {
        transformResponse: async (response) => {
          if (response.status === 204) {
            return { success: true };
          }
          return response.json();
        }
      });
      return { success: true };
    } catch (error) {
      console.error('Error removing favorite:', error);
      return { success: false };
    }
  },

  /**
   * Check if an issue is favorited by the current user
   */
  isIssueFavorited: (issueId: number) =>
    api.get<{ is_favorite: boolean; favorite_id: number | null }>(
      `${FAVORITES_BASE}/check?issue_id=${issueId}`
    ),

  /**
   * Toggle favorite status of an issue
   */
  toggleFavorite: async (issueId: number) => {
    const { is_favorite, favorite_id } = await favoritesApi.isIssueFavorited(issueId);
    
    if (is_favorite && favorite_id) {
      return favoritesApi.removeFavorite(favorite_id);
    } else {
      return favoritesApi.addFavorite(issueId);
    }
  },

  /**
   * Get favorite details including the issue
   */
  getFavoriteWithDetails: (favoriteId: number) =>
    api.get<Favorite & { issue: Issue }>(`${FAVORITES_BASE}/${favoriteId}/details`),
};
