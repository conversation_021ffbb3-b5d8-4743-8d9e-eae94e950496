# Exception Tracker (Extrack)

## Project Overview
Extrack is a comprehensive exception tracking and knowledge management system designed to help development teams track, manage, and resolve software exceptions and issues efficiently. The application provides a centralized platform for documenting issues, solutions, and knowledge base articles, making it easier for teams to find solutions to common problems and share knowledge.

## Key Features
- **Issue Tracking**: Log and track software exceptions and issues
- **Knowledge Base**: Create and maintain a searchable knowledge base
- **Solution Management**: Document and share solutions to common problems
- **Project Management**: Organize issues and knowledge by projects
- **User Management**: Role-based access control for different team members
- **Search Functionality**: Powerful search across issues and knowledge base
- **API Access**: RESTful API for integration with other tools

## Technology Stack

### Frontend
- **Framework**: Next.js with TypeScript
- **UI Components**: Radix UI Primitives
- **Styling**: Tailwind CSS
- **State Management**: React Context API
- **Form Handling**: React Hook Form with Zod validation

### Backend
- **Framework**: FastAPI (Python)
- **Database**: SQL (SQLAlchemy ORM)
- **Authentication**: JWT (JSON Web Tokens)
- **API Documentation**: OpenAPI (Swagger UI)

### Development Tools
- **Version Control**: Git
- **Package Manager**: npm / yarn
- **Containerization**: Docker

## Getting Started

### Prerequisites
- Node.js (v16+)
- Python (3.8+)
- PostgreSQL (or preferred SQL database)
- npm or yarn

### Installation
1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/extrack.git
   cd extrack
   ```

2. Install frontend dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Install Python dependencies:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   - Copy `.env.example` to `.env` in both root and backend directories
   - Update the configuration as needed

5. Run database migrations:
   ```bash
   cd backend
   alembic upgrade head
   ```

### Running the Application

#### Development Mode
1. Start the frontend development server:
   ```bash
   npm run dev:frontend
   ```

2. In a separate terminal, start the backend server:
   ```bash
   npm run dev:backend
   ```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

#### Production Build
```bash
# Build the frontend
npm run build

# Start the production server
npm start
```

## Documentation

For detailed documentation, please refer to the following:

- [Architecture](./docs/architecture/overview.md)
- [API Reference](./docs/api/README.md)
- [Development Guide](./docs/development.md)
- [Deployment Guide](./docs/deployment.md)

## Contributing

Contributions are welcome! Please read our [Contributing Guidelines](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, please open an issue in the GitHub repository or contact the development team.
