"use client"

import { Suspense, useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { useSession } from "next-auth/react"
import { SearchBar } from "@/components/SearchBar"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { KnowledgeList } from "@/components/knowledge-list"
import { KnowledgeDialog } from "@/components/knowledge-dialog"
import { LoadingSpinner } from "@/components/ui/loading-spinner"


function KnowledgeContent() {
  const searchParams = useSearchParams()
  const { data: session, status } = useSession()
  const [projectId, setProjectId] = useState<number | null>(null) // Will be set from URL parameter
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [activeTab, setActiveTab] = useState<string>("all")
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshTrigger, setRefreshTrigger] = useState<number>(0)

  // Update project ID when URL changes
  useEffect(() => {
    console.log("Updating project ID from URL");
    const projectParam = searchParams.get("project");
    console.log("Project parameter from URL:", projectParam);
    
    if (!projectParam) {
      setError("Please select a project to view knowledge entries");
      setIsLoading(false);
      return;
    }
    
    const id = parseInt(projectParam, 10);
    console.log("Parsed project ID:", id);
    
    if (isNaN(id)) {
      setError("Invalid project ID");
      setIsLoading(false);
      return;
    }
    
    console.log("Setting project ID from URL:", id);
    setProjectId(id);

    // Handle search query
    const queryParam = searchParams.get("q")
    if (queryParam) {
      setSearchQuery(queryParam)
    } else {
      setSearchQuery("")
    }
  }, [searchParams])

  // Check authentication and fetch knowledge entries
  useEffect(() => {
    if (status === "loading") {
      setIsLoading(true)
      setError(null)
    } else if (status === "authenticated" && session?.user?.accessToken) {
      setIsLoading(false)
      setError(null)
    } else if (status === "unauthenticated") {
      setError("Please log in to view knowledge entries")
      setIsLoading(false)
    } else if (status === "authenticated" && !session?.user?.accessToken) {
      setError("Authentication token is missing. Please try logging in again.")
      setIsLoading(false)
    }
  }, [status, session])

  // Handle search submission from SearchBar
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    // Update URL to reflect search query
    const newUrl = new URL(window.location.href)
    if (query.trim()) {
      newUrl.searchParams.set('q', query)
    } else {
      newUrl.searchParams.delete('q')
    }
    window.history.pushState({}, '', newUrl.toString())
  }

  // Handle search changes from KnowledgeList component
  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
    // Update URL to reflect search query
    const newUrl = new URL(window.location.href)
    if (query.trim()) {
      newUrl.searchParams.set('q', query)
    } else {
      newUrl.searchParams.delete('q')
    }
    window.history.pushState({}, '', newUrl.toString())
  }

  // Function to fetch knowledge entries
  const fetchKnowledgeEntries = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <div className="container mx-auto p-4 md:p-6 max-w-7xl">
      <div className="flex flex-col gap-4 md:gap-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Knowledge Bank</h1>
            <p className="text-muted-foreground">Search and contribute to the solution repository</p>
          </div>
          <KnowledgeDialog
            onEntryCreated={fetchKnowledgeEntries}
            buttonText="Add Knowledge Entry"
          />
        </div>

        <div className="flex flex-col md:flex-row gap-4 items-start">
          <div className="w-full md:flex-1">
            <SearchBar
              project_id={projectId ?? 0}
              type="knowledge"
              initialQuery={searchQuery}
              onSearch={handleSearch}
            />
          </div>
        </div>

        <Card className="overflow-hidden">
          <CardContent className="p-0">
            <Tabs
              defaultValue="all"
              className="w-full"
              value={activeTab}
              onValueChange={setActiveTab}
            >
              <div className="border-b px-4 pt-2">
                <TabsList className="mb-0 bg-transparent justify-start w-full">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="favorites">Favorites</TabsTrigger>
                </TabsList>
              </div>

              <div className="p-4">
                {error ? (
                  <div className="text-center py-8 text-red-500">
                    {error}
                  </div>
                ) : isLoading ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Loading knowledge entries...</p>
                  </div>
                ) : !projectId ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Please select a project to view knowledge entries</p>
                  </div>
                ) : (
                  <>
                    <TabsContent value="all" className="m-0">
                      <KnowledgeList
                        projectId={projectId ?? undefined}
                        searchQuery={searchQuery}
                        onSearchChange={handleSearchChange}
                        refreshTrigger={refreshTrigger}
                        filter="all"
                        category="all"
                      />
                    </TabsContent>
                    <TabsContent value="favorites" className="m-0">
                      <KnowledgeList
                        projectId={projectId ?? undefined}
                        searchQuery={searchQuery}
                        onSearchChange={handleSearchChange}
                        refreshTrigger={refreshTrigger}
                        filter="favorites"
                        category="all"
                      />
                    </TabsContent>
                  </>
                )}
              </div>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default function KnowledgePage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen"><LoadingSpinner /></div>}>
      <KnowledgeContent />
    </Suspense>
  )
}