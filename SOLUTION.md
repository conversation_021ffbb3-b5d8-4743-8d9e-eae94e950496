# Fix for SyntaxError: "Unexpected token '<', "<!DOCTYPE "... is not valid JSON"

## Problem Summary

The React frontend is receiving HTML responses (starting with `<!DOCTYPE`) when it expects JSON data from the API. This typically occurs when:

1. The backend server is not running
2. API endpoints return HTML error pages instead of JSON
3. Environment variables are misconfigured
4. Authentication issues cause redirects to HTML login pages

## Root Cause Analysis

Based on code analysis, the issue stems from:

1. **Missing Environment Variable**: The API route expects `BACKEND_URL` but only `NEXT_PUBLIC_API_URL` was configured
2. **Backend Server Not Running**: The FastAPI backend on port 8000 is not accessible
3. **Poor Error Handling**: The frontend doesn't gracefully handle HTML responses

## Solutions Implemented

### 1. ✅ Fixed Environment Variables

Updated `.env.local` to include the missing `BACKEND_URL`:

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
BACKEND_URL=http://localhost:8000  # Added this line
```

### 2. ✅ Improved Error Handling

Enhanced the API client (`lib/api/client.ts`) to:
- Detect HTML responses vs JSON responses
- Provide clear error messages when receiving HTML
- Handle connection failures gracefully

Enhanced the knowledge base API (`lib/api/knowledgeBase.ts`) to:
- Check content-type headers before parsing JSON
- Throw descriptive errors for HTML responses

Enhanced the Next.js API route (`app/api/knowledge-base/route.ts`) to:
- Add connection timeout (10 seconds)
- Better error handling for backend connection failures
- Validate response content-type before parsing

### 3. 🔧 Backend Server Setup

The backend requires Python dependencies. To set up:

```bash
# Install Python dependencies
pip install -r requirements.txt

# Start the backend server
python run.py
```

**Note**: If you encounter compilation errors on Windows, try:
```bash
# Use pre-compiled packages
pip install --only-binary=all -r requirements.txt
```

## Quick Fix Steps

### Step 1: Start the Backend Server

```bash
# In the project root directory
python run.py
```

The server should start on `http://localhost:8000`. You should see:
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
```

### Step 2: Start the Frontend Server

```bash
# In another terminal
npm run dev:frontend
```

### Step 3: Test the Connection

Run the debug script:
```bash
node debug-api.js
```

This will test connectivity to both servers and help identify remaining issues.

## Verification

1. **Backend Health Check**: Visit `http://localhost:8000/docs` - should show FastAPI documentation
2. **Frontend Access**: Visit `http://localhost:3000` - should load without console errors
3. **API Connectivity**: Check browser dev tools Network tab for successful API calls

## Common Issues & Solutions

### Issue: "Backend server is not responding"
**Solution**: Ensure the backend server is running on port 8000

### Issue: "Authentication required" 
**Solution**: Make sure you're logged in and the session is valid

### Issue: Python package compilation errors
**Solution**: 
- Use pre-compiled packages: `pip install --only-binary=all -r requirements.txt`
- Or use Docker for development
- Or set up WSL2 on Windows

### Issue: Port conflicts
**Solution**: 
- Check if ports 3000 and 8000 are available
- Update port numbers in configuration if needed

## Monitoring

The improved error handling now provides better debugging information:
- Check browser console for detailed error messages
- Check server logs for backend connection issues
- Use the debug script to test connectivity

## Next Steps

1. Ensure both servers are running consistently
2. Set up proper database connections for the backend
3. Configure authentication properly
4. Consider using Docker for easier development setup
