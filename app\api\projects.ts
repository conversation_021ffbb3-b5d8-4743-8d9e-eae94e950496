import { getSession } from 'next-auth/react';
import { API_BASE_URL } from '@/lib/api';
import { Project } from '@/types/project';

interface ExtendedSession {
  user: {
    accessToken: string;
    [key: string]: any;
  };
}

export async function getProjects() {
  const session = await getSession() as ExtendedSession | null;
  
  const headers: HeadersInit = {
    'Content-Type': 'application/json'
  };

  const response = await fetch(`${API_BASE_URL}/projects`, { headers });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Projects API error (${response.status}):`, errorText);
    throw new Error(`Failed to fetch projects: ${response.status} ${errorText}`);
  }

  return response.json();
}

/**
 * Fetches projects with user counts from the API
 * @returns Promise with array of Project objects
 */
export async function getProjectsWithUsers(): Promise<Project[]> {
  const session = await getSession() as ExtendedSession | null;
  
  const headers: HeadersInit = {
    'Content-Type': 'application/json'
  };

  // Add authorization header if session exists
  if (session?.user?.accessToken) {
    headers['Authorization'] = `Bearer ${session.user.accessToken}`;
  }

  const response = await fetch(`${API_BASE_URL}/projects/with-users/`, { 
    method: 'GET',
    headers 
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`Projects API error (${response.status}):`, errorText);
    throw new Error(`Failed to fetch projects with users: ${response.status} ${errorText}`);
  }

  return response.json();
}