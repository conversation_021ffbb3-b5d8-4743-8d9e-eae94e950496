// This is a legacy compatibility layer that re-exports the new API structure
// to maintain backward compatibility with existing code.
// It's recommended to update imports to use the new modules directly.

// Import the API client
import api from './client';

// Import API clients with legacy names
import { usersApi as userApi } from './users';
import { projectsApi as projectApi } from './projects';
import { issuesApi as issueApi } from './issues';
import { solutionsApi as solutionApi } from './solutions';
import { knowledgeBaseApi } from './knowledgeBase';
import { favoritesApi } from './favorites';

// Import types and enums from types
import {
  type User,
  type Project,
  type Issue,
  type Solution,
  type KnowledgeBase,
  type Favorite,
  type SearchFilters,
  type PaginatedResponse,
  StatusEnum,
  CategoryEnum,
  ImpactEnum,
  FrequencyEnum,
  SystemNameEnum,
} from './types';

// Export API clients
export { userApi, projectApi, issueApi, solutionApi, knowledgeBaseApi, favoritesApi };

// Export the API base URL for backward compatibility
export const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

// Legacy functions that need special handling

/**
 * Toggle favorite status of a knowledge base entry (legacy function)
 * @deprecated Use knowledgeBaseApi.toggleFavorite() instead
 */
export const toggleFavoriteKnowledge = async (kbId: number): Promise<void> => {
  try {
    await knowledgeBaseApi.toggleFavorite(kbId);
  } catch (error) {
    console.error('Failed to toggle favorite:', error);
    throw error;
  }
};

/**
 * Toggle like on a knowledge base entry (legacy function)
 * @deprecated Use knowledgeBaseApi.toggleLike() instead
 */
export const likeKnowledgeEntry = async (kbId: number): Promise<void> => {
  try {
    await knowledgeBaseApi.toggleLike(kbId);
  } catch (error) {
    console.error('Failed to toggle like:', error);
    throw error;
  }
};

// Re-export the API client for backward compatibility
export { api };

// For backward compatibility
type ApiResponse<T> = PaginatedResponse<T>;

// Export API error class for backward compatibility
export class ApiError extends Error {
  status: number;
  isAuthError: boolean;

  constructor(message: string, status: number) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.isAuthError = status === 401;
  }
}

// Export types and enums
export type {
  User,
  Project,
  Issue,
  Solution,
  KnowledgeBase,
  Favorite,
  SearchFilters,
  PaginatedResponse,
  ApiResponse,
};

export {
  StatusEnum,
  CategoryEnum,
  ImpactEnum,
  FrequencyEnum,
  SystemNameEnum,
};

// Export the API client as default
export default api;
