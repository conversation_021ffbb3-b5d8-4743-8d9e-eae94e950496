import NextAuth, { NextAuthOptions, User, Session } from "next-auth"
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials"
import { JWT } from "next-auth/jwt"

interface ExtendedUser extends User {
  id: string
  accessToken: string
  role: string
  name: string
  is_admin: boolean
}

interface ExtendedJWT extends JWT {
  accessToken: string
  role: string
  is_admin: boolean
  name: string
}

interface ExtendedSession extends Session {
  user: {
    id: string
    name: string
    email: string
    accessToken: string
    role: string
    is_admin: boolean
    project_ids?: number[]
  }
  expires: string
}

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

export const authOptions: NextAuthOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials): Promise<ExtendedUser | null> {
        if (!credentials?.username || !credentials?.password) {
          return null
        }

        try {
          const response = await fetch(`${API_BASE_URL}/token`, {
            method: "POST",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            body: new URLSearchParams({
              username: credentials.username,
              password: credentials.password,
            }),
            credentials: 'include',
          })

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("Auth error:", errorData);
            return null;
          }

          // Parse response data
          const data = await response.json()
          console.log('Auth response data:', data)
          
          // Make sure to properly handle the admin status
          const isAdmin = data.is_admin === true
          console.log('Auth - admin status:', { isAdmin, dataIsAdmin: data.is_admin })

          return {
            id: data.user_id.toString(),
            email: credentials.username,
            name: data.name || credentials.username,
            accessToken: data.access_token,
            role: isAdmin ? 'admin' : 'user',
            is_admin: isAdmin, // Explicitly set is_admin property
          }
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      // Initial sign in
      if (user) {
        const extendedUser = user as ExtendedUser
        // Check both possible locations for admin status
        const isAdmin = extendedUser.role === 'admin' || extendedUser.is_admin === true
        
        console.log('JWT callback - new sign in:', { 
          user, 
          isAdmin, 
          userRole: extendedUser.role,
          userIsAdmin: extendedUser.is_admin
        })
        
        return {
          ...token,
          accessToken: extendedUser.accessToken,
          role: extendedUser.role,
          is_admin: isAdmin,
          name: extendedUser.name,
          email: extendedUser.email, // Ensure email is in the token
        }
      }

      // Handle token updates from session updates
      if (trigger === "update") {
        // Check if we're updating the access token
        if (session?.user?.accessToken) {
          console.log('JWT callback - updating token with new access token')
          return {
            ...token,
            accessToken: session.user.accessToken,
          }
        }
        
        // Check for other session updates
        if (session) {
          console.log('JWT callback - updating token with session data')
          return {
            ...token,
            ...session,
          }
        }
      }

      // Return previous token if the access token has not expired yet
      return token
    },
    async session({ session, token }) {
      const extendedToken = token as ExtendedJWT
      
      // Make sure to check both possible locations for admin status
      const isAdmin = extendedToken.role === 'admin' || token.is_admin === true
      
      console.log('Session callback - setting admin status:', { 
        isAdmin, 
        tokenRole: extendedToken.role, 
        tokenIsAdmin: token.is_admin 
      })
      
      return {
        ...session,
        user: {
          ...session.user,
          id: token.sub || "",
          name: token.name,
          accessToken: extendedToken.accessToken,
          role: extendedToken.role,
          is_admin: isAdmin,
        },
      } as ExtendedSession
    }
  },
  events: {
    async signIn({ user }) {
      console.log("User signed in:", user.email);
    },
    async signOut() {
      console.log("User signed out");
    },
  },
  pages: {
    signIn: "/auth/login",
    signOut: "/auth/login",
    error: "/auth/login", // Error code passed in query string as ?error=
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  debug: process.env.NODE_ENV === "development",
}

const handler = NextAuth(authOptions)

export { handler as GET, handler as POST }
