"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { useEffect, useState } from "react"
import { IssueDetailsDialog } from "@/components/issue-details-dialog"
import { MappedIssue } from "./issue-details-dialog"
import { CategoryEnum } from "@/lib/api"

interface RecentIssuesProps {
  projectId?: number
}

export function RecentIssues({ projectId }: RecentIssuesProps) {
  const [issues, setIssues] = useState<MappedIssue[]>([]); // Change to MappedIssue[]
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedIssue, setSelectedIssue] = useState<MappedIssue | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    const fetchIssues = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/issues/recent?limit=5${projectId ? `&project_id=${projectId}` : ''}`);
        if (!response.ok) {
          throw new Error('Failed to fetch issues');
        }
        const data = await response.json();
        // Map the data to match the MappedIssue interface
        const mappedIssues: MappedIssue[] = data.map((issue: any) => ({
          id: issue.issue_id,
          title: issue.title,
          error_code: issue.error_code || "",
          error_message: issue.error_message || "",
          system_name: issue.system_name || "",
          issue_type: issue.issue_type || "",
          status: issue.status,
          impact: issue.impact || "",
          frequency: issue.frequency || "",
          category: issue.category || CategoryEnum.CommonIssues,
          jira_id: issue.jira_id || null,
          jira_link: issue.jira_link || null,
          hemants_view: issue.hemants_view || null,
          description: issue.description || "",
          createdAt: issue.created_at,
          createdBy: typeof issue.created_by === "string" ? issue.created_by : "", // <-- Ensure string
          comments: issue.comments || 0,
          solution: {
            exists: false, // Default value, update as needed
          },
        }));
        setIssues(mappedIssues);
      } catch (err) {
        console.error('Error fetching issues:', err);
        setError(err instanceof Error ? err.message : 'Failed to load issues');
      } finally {
        setIsLoading(false);
      }
    };

    fetchIssues();
  }, [projectId]);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error && issues.length === 0) {
    return (
      <div className="py-4 text-center text-muted-foreground">
        <p>Could not load issues. {error}</p>
      </div>
    );
  }

  if (issues.length === 0) {
    return (
      <div className="py-4 text-center text-muted-foreground">
        <p>No recent issues found.</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {issues.map((issue) => (
        <Card
          key={issue.id}
          className="overflow-hidden cursor-pointer hover:bg-muted/50 transition-colors"
          onClick={() => {
            setSelectedIssue(issue); // Now issue is of type MappedIssue
            setIsDialogOpen(true);
          }}
        >
          <CardContent className="p-3">
            <div className="flex flex-col gap-2">
              <div className="flex items-start justify-between">
                <h3 className="font-medium">{issue.title}</h3>
                <Badge
                  className={cn(
                    "ml-2 whitespace-nowrap",
                    issue.status === "Open" && "bg-orange-500 hover:bg-orange-600",
                    issue.status === "In Progress" && "bg-blue-500 hover:bg-blue-600",
                    issue.status === "Resolved" && "bg-green-500 hover:bg-green-600",
                  )}
                >
                  {issue.status}
                </Badge>
              </div>
              <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                {issue.system_name && (
                  <span className="inline-flex items-center rounded-full border px-2 py-0.5">{issue.system_name}</span>
                )}
                {issue.issue_type && (
                  <span className="inline-flex items-center rounded-full border px-2 py-0.5">{issue.issue_type}</span>
                )}
                {issue.impact && (
                  <span className="inline-flex items-center rounded-full border px-2 py-0.5">Impact: {issue.impact}</span>
                )}
                
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
      {selectedIssue && (
        <IssueDetailsDialog
          issue={selectedIssue}
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          onToggleFavorite={() => Promise.resolve()}
          isFavorite={false}
        />
      )}
    </div>
  );
}

