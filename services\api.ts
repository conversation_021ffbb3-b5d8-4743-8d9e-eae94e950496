const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000'

export const userApi = {
  async getCurrentUser() {
    const response = await fetch(`${API_BASE_URL}/users`, {
      credentials: "include",
      headers: {
        "Authorization": `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    if (!response.ok) {
      throw new Error("Failed to fetch user data")
    }
    return response.json()
  },
}