import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { API_BASE_URL } from '@/lib/api';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const projectId = searchParams.get('project_id');
  const limit = searchParams.get('limit') || '10';
  const sort = searchParams.get('sort') || 'created_at_desc';
  
  if (!projectId) {
    return NextResponse.json(
      { error: 'Project ID is required' },
      { status: 400 }
    );
  }

  try {
    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Make request to backend API
    const response = await fetch(
      `${API_BASE_URL}/projects/${projectId}/issues?limit=${limit}&sort=${sort}`,
      {
        headers: {
          'Authorization': `Bearer ${token['accessToken']}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error('Failed to fetch issues');
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching issues:', error);
    return NextResponse.json(
      { error: 'Failed to fetch issues' },
      { status: 500 }
    );
  }
}
