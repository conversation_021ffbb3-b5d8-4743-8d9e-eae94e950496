from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_
from typing import List, Optional
from ..database import get_db
from ..models import Issue, KnowledgeBase

router = APIRouter()

@router.get("/issues")
async def search_all_issues(
    query: Optional[str] = None,
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    Search issues across all projects by query string.
    If query is None or empty, returns all issues.

    Args:
        query (Optional[str]): Search query string to filter issues
        page (int): Page number for pagination, minimum 1
        per_page (int): Number of items per page, between 1 and 100
        db (Session): Database session dependency

    Returns:
        dict: {
            "items": List of issue dictionaries containing issue details,
            "total": Total number of matching issues,
            "page": Current page number,
            "per_page": Number of items per page
        }

    Raises:
        HTTPException: 500 error if database operation fails
    """
    try:
        # Start with a base query for all issues
        base_query = db.query(Issue)

        # Apply search filter if query is provided
        if query and query.strip():
            search_filter = or_(
                Issue.title.ilike(f"%{query}%"),
                Issue.error_message.ilike(f"%{query}%"),
                Issue.description.ilike(f"%{query}%"),
                Issue.error_code.ilike(f"%{query}%")
            )
            base_query = base_query.filter(search_filter)

        # Get total count for pagination
        total = base_query.count()

        # Apply pagination
        issues = base_query.offset((page - 1) * per_page).limit(per_page).all()

        # Format the response
        items = []
        for issue in issues:
            items.append({
                "issue_id": issue.issue_id,
                "title": issue.title,
                "error_message": issue.error_message,
                "description": issue.description,
                "category": issue.category,
                "status": issue.status,
                "created_at": issue.created_at
            })

        return {
            "items": items,
            "total": total,
            "page": page,
            "per_page": per_page
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/knowledge-base")
async def search_all_knowledge_base(
    query: Optional[str] = None,
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    Search knowledge base entries across all projects by query string.
    If query is None or empty, returns all knowledge base entries.

    Args:
        query (Optional[str]): Search query string to filter entries
        page (int): Page number for pagination, minimum 1
        per_page (int): Number of items per page, between 1 and 100
        db (Session): Database session dependency

    Returns:
        dict: {
            "items": List of knowledge base entry dictionaries containing entry details,
            "total": Total number of matching entries,
            "page": Current page number,
            "per_page": Number of items per page
        }

    Raises:
        HTTPException: 500 error if database operation fails
    """
    try:
        # Start with a base query for all knowledge base entries
        base_query = db.query(KnowledgeBase)

        # Apply search filter if query is provided
        if query and query.strip():
            search_filter = or_(
                KnowledgeBase.title.ilike(f"%{query}%"),
                KnowledgeBase.content.ilike(f"%{query}%")
            )
            base_query = base_query.filter(search_filter)

        # Get total count for pagination
        total = base_query.count()

        # Apply pagination
        kb_entries = base_query.offset((page - 1) * per_page).limit(per_page).all()

        # Format the response
        items = []
        for kb in kb_entries:
            items.append({
                "kb_id": kb.kb_id,
                "title": kb.title,
                "content": kb.content,
                "created_at": kb.created_at
            })

        return {
            "items": items,
            "total": total,
            "page": page,
            "per_page": per_page
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))