#!/usr/bin/env node

/**
 * Debug script to test API connectivity
 * Run with: node debug-api.js
 */

import http from 'http';
import https from 'https';

async function testEndpoint(url, description) {
  console.log(`\n🔍 Testing ${description}: ${url}`);
  
  return new Promise((resolve) => {
    const client = url.startsWith('https') ? https : http;
    
    const req = client.get(url, (res) => {
      console.log(`   Status: ${res.statusCode}`);
      console.log(`   Content-Type: ${res.headers['content-type']}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.headers['content-type']?.includes('application/json')) {
          try {
            const json = JSON.parse(data);
            console.log(`   ✅ Valid JSON response`);
            console.log(`   Response preview:`, JSON.stringify(json).substring(0, 100) + '...');
          } catch (e) {
            console.log(`   ❌ Invalid JSON:`, e.message);
          }
        } else {
          console.log(`   ⚠️  Non-JSON response (${data.length} chars)`);
          console.log(`   Preview:`, data.substring(0, 100) + '...');
        }
        resolve();
      });
    });
    
    req.on('error', (err) => {
      console.log(`   ❌ Connection failed:`, err.message);
      resolve();
    });
    
    req.setTimeout(5000, () => {
      console.log(`   ⏰ Request timeout`);
      req.destroy();
      resolve();
    });
  });
}

async function main() {
  console.log('🚀 API Connectivity Debug Tool');
  console.log('================================');
  
  // Test backend directly
  await testEndpoint('http://localhost:8000/docs', 'Backend API Docs');
  await testEndpoint('http://localhost:8000/knowledge-base?project_id=1', 'Backend Knowledge Base (no auth)');
  
  // Test Next.js API routes
  await testEndpoint('http://localhost:3000/api/knowledge-base?project_id=1', 'Next.js API Route');
  
  console.log('\n📋 Summary:');
  console.log('- If backend tests fail: Start the backend server with `python run.py`');
  console.log('- If Next.js tests fail: Start the frontend with `npm run dev:frontend`');
  console.log('- If you see HTML responses: Check server configuration and authentication');
  console.log('\n💡 Next steps:');
  console.log('1. Ensure both servers are running');
  console.log('2. Check environment variables in .env.local');
  console.log('3. Verify authentication is working');
}

main().catch(console.error);
