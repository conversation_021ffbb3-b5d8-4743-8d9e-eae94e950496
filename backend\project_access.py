from fastapi import Depends, HTTPException
from sqlalchemy.orm import Session
from .database import get_db
from . import models
from .auth import get_current_user

async def verify_project_access(
    project_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # Check if user is admin
    if current_user.is_admin:
        return True
    
    # Check if user has access to the project
    project_access = db.query(models.ProjectAccess).filter(
        models.ProjectAccess.project_id == project_id,
        models.ProjectAccess.user_id == current_user.id
    ).first()
    
    if not project_access:
        raise HTTPException(
            status_code=403,
            detail="You don't have access to this project"
        )
    
    return True