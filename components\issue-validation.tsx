"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, CheckCircle, XCircle } from "lucide-react"
import {Dialog,DialogContent,DialogDescription,DialogFooter,DialogHeader,DialogTitle,DialogTrigger} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

const mockPendingIssues = [
  {
    id: 1,
    title: "Database connection timeout in Order Processing",
    system: "OMS",
    type: "Database",
    impact: "High",
    submittedBy: "<EMAIL>",
    submittedAt: "2023-03-15T10:30:00Z",
  },
   {
    id: 2,
    title: "API authentication failure in inventory sync",
    system: "WMS",
    type: "API",
    impact: "Medium",
    submittedBy: "<EMAIL>",
    submittedAt: "2023-03-14T15:45:00Z",
  },
  {
    id: 3,
    title: "UI rendering error in product catalog",
    system: "OMS",
    type: "UI",
    impact: "Low",
    submittedBy: "<EMAIL>",
    submittedAt: "2023-03-13T09:15:00Z",
  },
  {
    id: 4,
    title: "Automation workflow stuck in pending state",
    system: "Automation",
    type: "Workflow",
    impact: "Critical",
    submittedBy: "<EMAIL>",
    submittedAt: "2023-03-12T14:20:00Z",
  },
]

export function IssueValidation() {
  const [pendingIssues, setPendingIssues] = useState(mockPendingIssues)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedIssue, setSelectedIssue] = useState<any>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)

  const filteredIssues = pendingIssues.filter(
    (issue) =>
      issue.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      issue.submittedBy.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleApproveIssue = (issueId: number) => {
    setPendingIssues(pendingIssues.filter((issue) => issue.id !== issueId))
    setIsDetailsDialogOpen(false)
  }

  const handleRejectIssue = (issueId: number) => {
    setPendingIssues(pendingIssues.filter((issue) => issue.id !== issueId))
    setIsDetailsDialogOpen(false)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search issues..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">{pendingIssues.length} pending issues</span>
        </div>
      </div>

      <Tabs defaultValue="pending">
        <TabsList>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>
        <TabsContent value="pending" className="mt-4">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>System</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Impact</TableHead>
                  <TableHead>Submitted By</TableHead>
                  <TableHead>Submitted At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredIssues.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4 text-muted-foreground">
                      No pending issues found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredIssues.map((issue) => (
                    <TableRow key={issue.id}>
                      <TableCell className="font-medium">{issue.title}</TableCell>
                      <TableCell>{issue.system}</TableCell>
                      <TableCell>{issue.type}</TableCell>
                      <TableCell>
                        <Badge
                          className={
                            issue.impact === "Critical"
                              ? "bg-red-500 hover:bg-red-600"
                              : issue.impact === "High"
                                ? "bg-orange-500 hover:bg-orange-600"
                                : issue.impact === "Medium"
                                  ? "bg-yellow-500 hover:bg-yellow-600"
                                  : "bg-green-500 hover:bg-green-600"
                          }
                        >
                          {issue.impact}
                        </Badge>
                      </TableCell>
                      <TableCell>{issue.submittedBy}</TableCell>
                      <TableCell>{formatDate(issue.submittedAt)}</TableCell>
                      <TableCell className="text-right">
                        <Dialog
                          open={isDetailsDialogOpen && selectedIssue?.id === issue.id}
                          onOpenChange={(open) => {
                            setIsDetailsDialogOpen(open)
                            if (!open) setSelectedIssue(null)
                          }}
                        >
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedIssue(issue)
                                setIsDetailsDialogOpen(true)
                              }}
                            >
                              Review
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-3xl">
                            <DialogHeader>
                              <DialogTitle>Review Issue</DialogTitle>
                              <DialogDescription>Review and approve or reject this issue.</DialogDescription>
                            </DialogHeader>
                            {selectedIssue && (
                              <div className="space-y-4 py-4">
                                <div>
                                  <h3 className="text-lg font-medium">{selectedIssue.title}</h3>
                                  <div className="mt-2 flex flex-wrap gap-2">
                                    <Badge>{selectedIssue.system}</Badge>
                                    <Badge variant="outline">{selectedIssue.type}</Badge>
                                    <Badge
                                      className={
                                        selectedIssue.impact === "Critical"
                                          ? "bg-red-500 hover:bg-red-600"
                                          : selectedIssue.impact === "High"
                                            ? "bg-orange-500 hover:bg-orange-600"
                                            : selectedIssue.impact === "Medium"
                                              ? "bg-yellow-500 hover:bg-yellow-600"
                                              : "bg-green-500 hover:bg-green-600"
                                      }
                                    >
                                      {selectedIssue.impact}
                                    </Badge>
                                  </div>
                                </div>
                                <div className="rounded-md bg-muted p-4">
                                  <h4 className="font-medium">Error Details</h4>
                                  <div className="mt-2 font-mono text-sm">
                                    <p>Error Code: ERR_DB_TIMEOUT</p>
                                    <p>Error Message: Connection to database timed out after 30 seconds</p>
                                  </div>
                                </div>
                                <div>
                                  <h4 className="font-medium">Description</h4>
                                  <p className="mt-2 text-muted-foreground">
                                    The order processing module is experiencing database connection timeouts during peak
                                    hours. This is causing orders to get stuck in the processing queue.
                                  </p>
                                </div>
                                <div className="flex justify-between text-sm text-muted-foreground">
                                  <span>Submitted by: {selectedIssue.submittedBy}</span>
                                  <span>Submitted at: {formatDate(selectedIssue.submittedAt)}</span>
                                </div>
                              </div>
                            )}
                            <DialogFooter>
                              <Button
                                variant="outline"
                                onClick={() => handleRejectIssue(selectedIssue?.id)}
                                className="gap-1"
                              >
                                <XCircle className="h-4 w-4" /> Reject
                              </Button>
                              <Button onClick={() => handleApproveIssue(selectedIssue?.id)} className="gap-1">
                                <CheckCircle className="h-4 w-4" /> Approve
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="approved" className="mt-4">
          <div className="text-center py-8">
            <p className="text-muted-foreground">Approved issues will appear here.</p>
          </div>
        </TabsContent>
        <TabsContent value="rejected" className="mt-4">
          <div className="text-center py-8">
            <p className="text-muted-foreground">Rejected issues will appear here.</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

