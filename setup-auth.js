#!/usr/bin/env node

/**
 * Setup script to ensure authentication is properly configured
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

function generateSecretKey() {
  return crypto.randomBytes(32).toString('hex');
}

function checkEnvFile() {
  const envPath = path.join(__dirname, '.env');
  const envExamplePath = path.join(__dirname, '.env.example');
  
  if (!fs.existsSync(envPath)) {
    console.log('Creating .env file from .env.example...');
    
    if (fs.existsSync(envExamplePath)) {
      let envContent = fs.readFileSync(envExamplePath, 'utf8');
      
      // Replace placeholder secret with generated one
      const secretKey = generateSecretKey();
      envContent = envContent.replace(
        'your-super-secret-key-here-change-this-in-production',
        secretKey
      );
      
      fs.writeFileSync(envPath, envContent);
      console.log('✅ .env file created with generated secret key');
    } else {
      console.log('❌ .env.example not found');
    }
  } else {
    console.log('✅ .env file already exists');
  }
}

function validateEnvVariables() {
  require('dotenv').config();
  
  const requiredVars = ['NEXTAUTH_SECRET'];
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.log('❌ Missing environment variables:', missing.join(', '));
    return false;
  }
  
  console.log('✅ All required environment variables are set');
  return true;
}

function main() {
  console.log('🔧 Setting up authentication configuration...\n');
  
  checkEnvFile();
  validateEnvVariables();
  
  console.log('\n✅ Authentication setup complete!');
  console.log('\n📝 Next steps:');
  console.log('1. Restart your development servers');
  console.log('2. Test the knowledge base creation functionality');
  console.log('3. Check the console logs for any remaining authentication errors');
}

if (require.main === module) {
  main();
}

module.exports = { checkEnvFile, validateEnvVariables };
