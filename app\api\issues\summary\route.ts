import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { API_BASE_URL } from '@/lib/api';

export async function GET(request: NextRequest) {
  try {
    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Extract project_id from query params if present
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('project_id');
    const backendUrl = `${API_BASE_URL}/api/issues/summary${projectId ? `?project_id=${projectId}` : ''}`;

    // Make request to backend API
    const response = await fetch(
      backendUrl,
      {
        headers: {
          'Authorization': `Bearer ${token['accessToken']}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error('Failed to fetch issue summary');
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching issue summary:', error);
    return NextResponse.json(
      { error: 'Failed to fetch issue summary' },
      { status: 500 }
    );
  }
} 