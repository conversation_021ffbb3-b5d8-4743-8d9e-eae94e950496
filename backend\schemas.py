from pydantic import BaseModel, EmailStr, validator
from datetime import datetime
from typing import Optional, List, Union
from .models import RoleEnum

class UserBase(BaseModel):
    name: str
    email: EmailStr
    password_hash: str

class UserCreate(UserBase):
    pass

class UserResponse(BaseModel):
    user_id: int
    name: str
    email: str
    created_at: datetime
    is_admin: bool = False

    class Config:
        from_attributes = True

class UserLogin(BaseModel):
    name: str
    email: EmailStr
    password: str
    role: str = "user"

class UserProfileResponse(BaseModel):
    id: str
    name: str
    email: str
    is_admin: bool = False

class UserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    role: Optional[str] = None
    status: Optional[str] = None
    projects: Optional[List[str]] = None

class IssueBase(BaseModel):
    jira_id: Optional[Union[str, int]] = None
    jira_link: Optional[Union[str, int]] = None

    @validator('jira_id', 'jira_link', pre=True)
    def convert_to_str(cls, v):
        if v is not None:
            return str(v)
        return v

class IssueCreate(IssueBase):
    pass

class IssueResponse(BaseModel):
    issue_id: int
    jira_id: str
    jira_link: str
    created_at: datetime
    created_by: int
    created_by_name: Optional[str] = None

    class Config:
        from_attributes = True

class IssueUpdate(BaseModel):
    jira_id: Optional[Union[str, int]] = None
    jira_link: Optional[Union[str, int]] = None