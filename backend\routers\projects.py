from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func
from ..database import get_db
from ..crud import get_all_projects, create_project
from .. import models
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
from ..dependencies import verify_project_access

router = APIRouter()

class ProjectBase(BaseModel):
    project_name: str
    description: str
    icon_name: str

class ProjectCreate(ProjectBase):
    pass

class ProjectResponse(BaseModel):
    id: int
    name: str
    user_count: int
    created_at: datetime
    status: str = "Active"

@router.get("/projects/")
async def read_projects(db: Session = Depends(get_db)):
    projects = get_all_projects(db)
    return [
        {
            "id": project.project_id,
            "title": project.project_name,
            "description": project.description,
            "icon_name": project.icon_name
        }
        for project in projects
    ]
@router.get("/projects/{project_id}/issues")
async def get_project_issues(
    project_id: int,
    limit: Optional[int] = 10,
    sort: Optional[str] = 'created_at_desc',
    db: Session = Depends(get_db),
    access = Depends(verify_project_access)
):
    # If we get here, the user has access
    query = db.query(models.Issue).filter(models.Issue.project_id == project_id)
    
    # Apply sorting
    if sort == 'created_at_desc':
        query = query.order_by(models.Issue.created_at.desc())
    elif sort == 'created_at_asc':
        query = query.order_by(models.Issue.created_at.asc())
    
    # Apply limit
    if limit:
        query = query.limit(limit)
    
    issues = query.all()
    return issues

@router.get("/projects/with-users/", response_model=List[ProjectResponse])
async def read_projects_with_users(db: Session = Depends(get_db)):
    """
    Get all projects with user counts.
    
    Returns a list of projects with the number of users assigned to each project.
    """
    # Query projects with user counts using a subquery
    projects_with_users = db.query(
        models.Project.project_id.label("id"),
        models.Project.project_name.label("name"),
        models.Project.created_at,
        func.count(models.UserProjectRole.user_id).label("user_count")
    ).outerjoin(
        models.UserProjectRole,
        models.Project.project_id == models.UserProjectRole.project_id
    ).group_by(
        models.Project.project_id
    ).all()
    
    # Format the response
    return [
        {
            "id": project.id,
            "name": project.name,
            "user_count": project.user_count,
            "created_at": project.created_at,
            "status": "Active"  # Placeholder status as requested
        }
        for project in projects_with_users
    ]

@router.post("/projects/", response_model=dict)
async def create_new_project(project: ProjectCreate, db: Session = Depends(get_db)):
    """
    Create a new project.
    
    Args:
        project: Project data
        db: Database session
        
    Returns:
        The newly created project
    """
    try:
        db_project = create_project(
            db, 
            project_name=project.project_name, 
            description=project.description, 
            icon_name=project.icon_name
        )
        
        return {
            "project_id": db_project.project_id,
            "project_name": db_project.project_name,
            "description": db_project.description,
            "icon_name": db_project.icon_name,
            "created_at": db_project.created_at
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Could not create project: {str(e)}")

@router.get("/projects/{project_id}", response_model=dict)
async def read_project(project_id: int, db: Session = Depends(get_db)):
    """
    Get a specific project by ID.
    
    Args:
        project_id: ID of the project to retrieve
        db: Database session
        
    Returns:
        The requested project
    """
    from ..crud import get_project
    
    project = get_project(db, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Count users in this project
    user_count = db.query(func.count(models.UserProjectRole.user_id)).filter(
        models.UserProjectRole.project_id == project_id
    ).scalar()
    
    return {
        "id": project.project_id,
        "name": project.project_name,
        "description": project.description,
        "icon_name": project.icon_name,
        "created_at": project.created_at,
        "user_count": user_count,
        "status": "Active"  # Placeholder status
    }