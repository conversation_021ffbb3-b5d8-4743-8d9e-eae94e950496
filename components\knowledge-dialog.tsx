'use client';

import { Suspense, useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, <PERSON><PERSON>Header, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@/contexts/user-context";
import { FileText, Save } from "lucide-react";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface KnowledgeEntry {
  id?: number;
  title: string;
  content: string;
  solution?: string;
  created_at?: string;
  created_by?: string;
}

interface KnowledgeDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onEntryCreated?: () => void;
  buttonText?: string;
  entry?: KnowledgeEntry | null;
  mode?: 'create' | 'view' | 'edit';
}

function KnowledgeDialogContent({ 
  open: externalOpen = false, 
  onOpenChange: setExternalOpen,
  onEntryCreated, 
  buttonText = "Add New Knowledge Entry",
  entry = null,
  mode = 'create'
}: KnowledgeDialogProps) {
  const { user } = useUser();
  const searchParams = useSearchParams();
  const [projectId, setProjectId] = useState<number | null>(null);
  const [title, setTitle] = useState(entry?.title || '');
  const [content, setContent] = useState(entry?.content || '');
  const [solution, setSolution] = useState(entry?.solution || '');
  const [isLoading, setIsLoading] = useState(false);
  const [internalOpen, setInternalOpen] = useState(false);
  const isControlled = setExternalOpen !== undefined;
  const isOpen = isControlled ? externalOpen : internalOpen;
  const { toast } = useToast();

  // Update form when entry changes
  useEffect(() => {
    if (entry) {
      setTitle(entry.title || '');
      setContent(entry.content || '');
      setSolution(entry.solution || '');
    }
  }, [entry]);

  // Update project ID when URL changes
  useEffect(() => {
    const projectParam = searchParams.get("project");
    if (projectParam) {
      const id = parseInt(projectParam, 10);
      if (!isNaN(id)) {
        setProjectId(id);
      }
    }
  }, [searchParams]);

  const handleOpenChange = (open: boolean) => {
    if (setExternalOpen) {
      setExternalOpen(open);
    } else {
      setInternalOpen(open);
    }
    
    if (!open) {
      // Reset form when dialog is closed
      setTitle(entry?.title || '');
      setContent(entry?.content || '');
      setSolution(entry?.solution || '');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!projectId) {
      toast({
        title: "Error",
        description: "No project selected",
        variant: "destructive",
      });
      return;
    }
    
    if (mode === 'view') {
      handleOpenChange(false);
      return;
    }
    
    setIsLoading(true);

    try {
      // Create the knowledge base entry with the solution text only
      const response = await fetch('/api/knowledge-base', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_id: projectId,
          title,
          content,
          solution,
          uploaded_by: user?.user_id
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create knowledge base entry');
      }

      toast({
        title: "Success",
        description: "Knowledge base entry created successfully",
      });

      // Reset form and close dialog
      setTitle("");
      setContent("");
      setSolution("");
      setInternalOpen(false);
      
      // Notify parent component
      if (onEntryCreated) {
        onEntryCreated();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create knowledge base entry",
        variant: "destructive",
      });
    }
  };

  const renderContent = () => (
    <div className="grid gap-4 py-4">
      <div className="grid gap-2">
        <Label htmlFor="title">Title</Label>
        {mode === 'view' ? (
          <div className="rounded-md border border-input px-3 py-2 text-sm">
            {title || 'No title'}
          </div>
        ) : (
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter a descriptive title"
            required
            disabled={isLoading}
          />
        )}
      </div>
      <div className="grid gap-2">
        <Label htmlFor="content">Content</Label>
        {mode === 'view' ? (
          <div className="rounded-md border border-input px-3 py-2 text-sm min-h-[120px] whitespace-pre-wrap">
            {content || 'No content'}
          </div>
        ) : (
          <Textarea
            id="content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Describe the issue or knowledge in detail"
            className="min-h-[120px]"
            required
            disabled={isLoading}
          />
        )}
      </div>
      <div className="grid gap-2">
        <Label htmlFor="solution">Solution {mode !== 'view' && '(Optional)'}</Label>
        {mode === 'view' ? (
          <div className="rounded-md border border-input px-3 py-2 text-sm min-h-[100px] whitespace-pre-wrap">
            {solution || 'No solution provided'}
          </div>
        ) : (
          <Textarea
            id="solution"
            value={solution}
            onChange={(e) => setSolution(e.target.value)}
            placeholder="Provide the solution if available"
            className="min-h-[100px]"
            disabled={isLoading}
          />
        )}
      </div>
      {mode === 'view' && entry?.created_by && entry?.created_at && (
        <div className="text-xs text-muted-foreground mt-2">
          Created by {entry.created_by} • {new Date(entry.created_at).toLocaleDateString()}
        </div>
      )}
    </div>
  );

  const dialogContent = (
    <DialogContent className="sm:max-w-[600px]">
      <form onSubmit={handleSubmit}>
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Add New Knowledge Entry' : 
             mode === 'edit' ? 'Edit Knowledge Entry' : 'Knowledge Entry Details'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'view' 
              ? 'View the details of this knowledge entry.'
              : 'Share your knowledge to help others solve similar issues.'}
          </DialogDescription>
        </DialogHeader>
        {renderContent()}
        <DialogFooter>
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => handleOpenChange(false)}
            disabled={isLoading}
          >
            {mode === 'view' ? 'Close' : 'Cancel'}
          </Button>
          {mode !== 'view' && ( 
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {mode === 'create' ? 'Create Entry' : 'Save Changes'}
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </form>
    </DialogContent>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      {!isControlled && (
        <DialogTrigger asChild>
          <Button>
            <FileText className="mr-2 h-4 w-4" />
            {buttonText}
          </Button>
        </DialogTrigger>
      )}
      {dialogContent}
    </Dialog>
  );
}

export function KnowledgeDialog(props: KnowledgeDialogProps) {
  return (
    <Suspense fallback={<LoadingSpinner className="m-4" />}>
      <KnowledgeDialogContent {...props} />
    </Suspense>
  );
}
