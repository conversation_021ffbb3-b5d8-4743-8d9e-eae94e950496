/**
 * Debug utility to check API configuration
 */

export const debugApiConfig = () => {
  console.log('=== API Configuration Debug ===');
  console.log('NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);
  console.log('BACKEND_URL:', process.env.BACKEND_URL);
  console.log('Current API_BASE_URL:', '/api');
  console.log('Expected frontend calls: /api/knowledge-base');
  console.log('Expected backend calls: http://localhost:8000/knowledge-base');
  console.log('================================');
};

// Call this in development to verify configuration
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  debugApiConfig();
}
