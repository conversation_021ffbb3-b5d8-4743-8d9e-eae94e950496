"use client"

import { Suspense, memo, useCallback, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Loader2, Search } from "lucide-react"
import { useRouter } from "next/navigation"
import { useSearch } from "@/hooks/use-search"
import type { SearchResult, SearchAPIResultType } from "@/types/search"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

interface SearchBarProps {
  placeholder?: string
  type?: SearchAPIResultType
  onResults?: (results: SearchResult[]) => void
  project_id?: number | undefined
  initialQuery?: string
  onSearch?: (query: string) => void
  className?: string
}

const SearchBarContent = ({
  placeholder = "Search...",
  type = "all",
  onResults,
  project_id = undefined,
  initialQuery = "",
  onSearch,
  className = ""
}: SearchBarProps) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const urlProjectId = searchParams.get('project') ? Number(searchParams.get('project')) : null
  
  // Convert project_id to a number if it exists and is valid, otherwise use urlProjectId
  const effectiveProjectId = project_id !== undefined && project_id !== null ? Number(project_id) : 
                            (urlProjectId !== null ? Number(urlProjectId) : null);
  
  const { query, setQuery, results, isLoading } = useSearch({
    projectId: effectiveProjectId,
    type: type,
    initialQuery: initialQuery
  })

  useEffect(() => {
    if (onResults && results) {
      // Transform API results to our UI format
      const typedResults = results.map(result => {
        // Map the API result to our UI result type
        const uiResult: SearchResult = {
          id: result.id,
          title: result.title,
          description: result.description,
          error_message: result.error_message,
          steps: result.steps,
          type: result.type === 'issue' ? 'issue' : 'knowledge'
        };
        return uiResult;
      });
      onResults(typedResults);
    }
  }, [onResults, results])

  const handleResultClick = useCallback((result: SearchResult) => {
    try {
      // Use project_id prop if available, otherwise fall back to URL param
      const targetProjectId = project_id ?? urlProjectId;
      
      if (result.type === "issue") {
        router.push(`/issues${targetProjectId ? `?project=${targetProjectId}` : ''}`)
      } else if (result.type === "knowledge") {
        router.push(`/knowledge${targetProjectId ? `?project=${targetProjectId}` : ''}`)
      } else {
        console.warn('Unknown result type:', result.type)
      }
    } catch (err) {
      console.error('Error navigating to result:', err)
    }
  }, [router, project_id, urlProjectId])

  // Handle form submission
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    if (onSearch && query.trim()) {
      onSearch(query.trim())
    }
  }, [onSearch, query])

  return (
    <div className={`relative w-full ${className}`}>
      <form onSubmit={handleSubmit}>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={isLoading ? "Searching..." : placeholder}
            className={`pl-10 pr-8 ${isLoading ? "opacity-70" : ""}`}
            disabled={isLoading}
            aria-busy={isLoading}
          />
          {isLoading && (
            <Loader2 className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 animate-spin text-muted-foreground" />
          )}
        </div>
      </form>

      {results.length > 0 && (
        <div 
          className="absolute top-full left-0 right-0 mt-2 bg-popover text-popover-foreground rounded-md shadow-lg z-50 max-h-96 overflow-y-auto border"
          role="listbox"
          aria-label="Search results"
        >
          {results.map((result) => (
            <button
              type="button"
              key={`${result.type || 'item'}-${result.id}`}
              className="w-full px-4 py-3 text-left hover:bg-accent hover:text-accent-foreground transition-colors"
              onClick={() => handleResultClick(result as SearchResult)}
              onKeyDown={(e) => e.key === 'Enter' && handleResultClick(result as SearchResult)}
              role="option"
              aria-selected="false"
            >
              <div className="flex flex-col gap-1">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-left">{result.title}</span>
                  {result.type && (
                    <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full capitalize">
                      {result.type}
                    </span>
                  )}
                </div>
                {result.description && (
                  <p className="text-sm text-muted-foreground mt-1 line-clamp-2 text-left">
                    {result.description}
                  </p>
                )}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}

// Export the memoized version of the component
const SearchBarComponent = memo(SearchBarContent);

export function SearchBar(props: SearchBarProps) {
  return (
    <Suspense fallback={<div className="flex items-center justify-center p-2"><LoadingSpinner className="h-5 w-5" /></div>}>
      <SearchBarComponent {...props} />
    </Suspense>
  );
}