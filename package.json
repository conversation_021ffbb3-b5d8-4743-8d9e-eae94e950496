{"name": "my-v0-project", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "next dev", "dev:backend": "python run.py", "build": "next build ", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.81.5", "@tiptap/core": "^2.2.4", "@types/js-cookie": "^3.0.6", "autoprefixer": "^10.4.18", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "cors": "^2.8.5", "curl": "^0.1.4", "date-fns": "^3.3.1", "embla-carousel-react": "^8.0.0", "framer-motion": "^12.6.3", "input-otp": "^1.1.0", "js-cookie": "^3.0.5", "lucide-react": "^0.358.0", "next": "^15.4.1", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "pytest": "^1.0.0", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "styled-components": "^6.1.18", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.13.15", "@types/react": "^18", "@types/react-dom": "^18", "concurrently": "^9.2.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}