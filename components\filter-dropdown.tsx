"use client"

import * as React from "react"
import { useState } from "react"
import { Button } from "../components/ui/button"
import { Checkbox } from "../components/ui/checkbox"
import { Label } from "../components/ui/label"
import { RadioGroup, RadioGroupItem } from "../components/ui/radio-group"
import { Separator } from "../components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "../components/ui/collapsible"
import { ChevronDown, ChevronUp, Filter, X } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "../components/ui/popover"
import { Badge } from "../components/ui/badge"

interface FilterDropdownProps {
  type: "issues" | "knowledge" | "all"
}

export function FilterDropdown({ type }: FilterDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeFilters, setActiveFilters] = useState<string[]>([])

  const [filters, setFilters] = useState({
    systems: {
      oms: false,
      wms: false,
      automation: false,
      other: false,
    },
    types: {
      database: false,
      api: false,
      ui: false,
      workflow: false,
      network: false,
      other: false,
    },
    impact: type === "issues" || type === "all" ? "all" : undefined,
    verification: type === "knowledge" || type === "all" ? "all" : undefined,
    dateRange: "all",
    sortBy: type === "knowledge" || type === "all" ? "relevance" : undefined,
  })

  // State for collapsible sections
  const [openSections, setOpenSections] = useState({
    systems: true,
    types: true,
    impact: true,
    verification: true,
    dateRange: true,
    sortBy: true,
  })

  const toggleSection = (section: keyof typeof openSections) => {
    setOpenSections({
      ...openSections,
      [section]: !openSections[section],
    })
  }

  const handleSystemChange = (system: keyof typeof filters.systems) => {
    const newValue = !filters.systems[system]
    setFilters({
      ...filters,
      systems: {
        ...filters.systems,
        [system]: newValue,
      },
    })

    updateActiveFilters(system, newValue, "System")
  }

  const handleTypeChange = (type: keyof typeof filters.types) => {
    const newValue = !filters.types[type]
    setFilters({
      ...filters,
      types: {
        ...filters.types,
        [type]: newValue,
      },
    })

    updateActiveFilters(type, newValue, "Type")
  }

  const handleImpactChange = (impact: string) => {
    setFilters({
      ...filters,
      impact,
    })

    if (impact !== "all") {
      updateActiveFilters("impact", true, "Impact", impact)
    } else {
      removeActiveFilter("impact")
    }
  }

  const handleVerificationChange = (verification: string) => {
    setFilters({
      ...filters,
      verification,
    })

    if (verification !== "all") {
      updateActiveFilters("verification", true, "Verification", verification)
    } else {
      removeActiveFilter("verification")
    }
  }

  const handleDateRangeChange = (dateRange: string) => {
    setFilters({
      ...filters,
      dateRange,
    })

    if (dateRange !== "all") {
      updateActiveFilters("dateRange", true, "Date", dateRange)
    } else {
      removeActiveFilter("dateRange")
    }
  }

  const handleSortByChange = (sortBy: string) => {
    setFilters({
      ...filters,
      sortBy,
    })
  }

  const updateActiveFilters = (key: string, isActive: boolean, category: string, displayValue?: string) => {
    if (isActive) {
      const filterLabel = displayValue
        ? `${category}: ${displayValue.charAt(0).toUpperCase() + displayValue.slice(1)}`
        : `${category}: ${key.charAt(0).toUpperCase() + key.slice(1)}`

      if (!activeFilters.includes(filterLabel)) {
        setActiveFilters([...activeFilters, filterLabel])
      }
    } else {
      removeActiveFilter(key, category)
    }
  }

  const removeActiveFilter = (key: string, category?: string) => {
    setActiveFilters(
      activeFilters.filter((filter) => {
        if (category) {
          return !filter.startsWith(`${category}: ${key.charAt(0).toUpperCase() + key.slice(1)}`)
        }
        return !filter.includes(key)
      }),
    )
  }

  const resetFilters = () => {
    setFilters({
      systems: {
        oms: false,
        wms: false,
        automation: false,
        other: false,
      },
      types: {
        database: false,
        api: false,
        ui: false,
        workflow: false,
        network: false,
        other: false,
      },
      impact: type === "issues" || type === "all" ? "all" : undefined,
      verification: type === "knowledge" || type === "all" ? "all" : undefined,
      dateRange: "all",
      sortBy: type === "knowledge" || type === "all" ? "relevance" : undefined,
    })
    setActiveFilters([])
  }

  const removeFilter = (filter: string) => {
    setActiveFilters(activeFilters.filter((f) => f !== filter))

    // Extract the category and value from the filter string
    const [category, value] = filter.split(": ")
    const lowerValue = value?.toLowerCase() ?? ''

    // Update the corresponding filter state
    if (category === "System" && lowerValue in filters.systems) {
      setFilters({
        ...filters,
        systems: {
          ...filters.systems,
          [lowerValue]: false,
        },
      })
    } else if (category === "Type" && lowerValue in filters.types) {
      setFilters({
        ...filters,
        types: {
          ...filters.types,
          [lowerValue]: false,
        },
      })
    } else if (category === "Impact") {
      setFilters({
        ...filters,
        impact: "all",
      })
    } else if (category === "Verification") {
      setFilters({
        ...filters,
        verification: "all",
      })
    } else if (category === "Date") {
      setFilters({
        ...filters,
        dateRange: "all",
      })
    }
  }

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center gap-2">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              Filters
              {activeFilters.length > 0 && (
                <Badge variant="secondary" className="ml-1 rounded-full">
                  {activeFilters.length}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 max-h-[80vh] overflow-auto" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Filters</h3>
                <Button variant="ghost" size="sm" onClick={resetFilters} className="h-8 px-2 text-xs">
                  Reset all
                </Button>
              </div>

              <Collapsible open={openSections.systems} onOpenChange={() => toggleSection("systems")}>
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">System/Module</h3>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
                      {openSections.systems ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                    </Button>
                  </CollapsibleTrigger>
                </div>
                <CollapsibleContent className="pt-2">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="system-oms"
                        checked={filters.systems.oms}
                        onCheckedChange={() => handleSystemChange("oms")}
                      />
                      <Label htmlFor="system-oms">Order Management System</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="system-wms"
                        checked={filters.systems.wms}
                        onCheckedChange={() => handleSystemChange("wms")}
                      />
                      <Label htmlFor="system-wms">Warehouse Management System</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="system-automation"
                        checked={filters.systems.automation}
                        onCheckedChange={() => handleSystemChange("automation")}
                      />
                      <Label htmlFor="system-automation">Automation Platform</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="system-other"
                        checked={filters.systems.other}
                        onCheckedChange={() => handleSystemChange("other")}
                      />
                      <Label htmlFor="system-other">Other</Label>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>

              <Separator />

              <Collapsible open={openSections.types} onOpenChange={() => toggleSection("types")}>
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Exception Type</h3>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
                      {openSections.types ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                    </Button>
                  </CollapsibleTrigger>
                </div>
                <CollapsibleContent className="pt-2">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="type-database"
                        checked={filters.types.database}
                        onCheckedChange={() => handleTypeChange("database")}
                      />
                      <Label htmlFor="type-database">Database</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="type-api"
                        checked={filters.types.api}
                        onCheckedChange={() => handleTypeChange("api")}
                      />
                      <Label htmlFor="type-api">API</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="type-ui"
                        checked={filters.types.ui}
                        onCheckedChange={() => handleTypeChange("ui")}
                      />
                      <Label htmlFor="type-ui">UI</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="type-workflow"
                        checked={filters.types.workflow}
                        onCheckedChange={() => handleTypeChange("workflow")}
                      />
                      <Label htmlFor="type-workflow">Workflow</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="type-network"
                        checked={filters.types.network}
                        onCheckedChange={() => handleTypeChange("network")}
                      />
                      <Label htmlFor="type-network">Network</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="type-other"
                        checked={filters.types.other}
                        onCheckedChange={() => handleTypeChange("other")}
                      />
                      <Label htmlFor="type-other">Other</Label>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>

              <Separator />

              {type === "issues" && (
                <>
                  <Collapsible open={openSections.impact} onOpenChange={() => toggleSection("impact")}>
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">Impact Level</h3>
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
                          {openSections.impact ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                        </Button>
                      </CollapsibleTrigger>
                    </div>
                    <CollapsibleContent className="pt-2">
                      <RadioGroup value={filters.impact || null} onValueChange={handleImpactChange} className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="all" id="impact-all" />
                          <Label htmlFor="impact-all">All</Label>
                        </div>  
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="critical" id="impact-critical" />
                          <Label htmlFor="impact-critical">Critical</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="high" id="impact-high" />
                          <Label htmlFor="impact-high">High</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="medium" id="impact-medium" />
                          <Label htmlFor="impact-medium">Medium</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="low" id="impact-low" />
                          <Label htmlFor="impact-low">Low</Label>
                        </div>
                      </RadioGroup>
                    </CollapsibleContent>
                  </Collapsible>

                  <Separator />
                </>
              )}

              {type === "knowledge" && (
                <>
                  <Collapsible open={openSections.verification} onOpenChange={() => toggleSection("verification")}>
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">Verification Status</h3>
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
                          {openSections.verification ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                        </Button>
                      </CollapsibleTrigger>
                    </div>
                    <CollapsibleContent className="pt-2">
                      <RadioGroup
                        value={filters.verification || null}
                        onValueChange={handleVerificationChange}
                        className="space-y-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="all" id="verification-all" />
                          <Label htmlFor="verification-all">All</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="verified" id="verification-verified" />
                          <Label htmlFor="verification-verified">Verified Only</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="community" id="verification-community" />
                          <Label htmlFor="verification-community">Community Contributions</Label>
                        </div>
                      </RadioGroup>
                    </CollapsibleContent>
                  </Collapsible>

                  <Separator />
                </>
              )}

              <Collapsible open={openSections.dateRange} onOpenChange={() => toggleSection("dateRange")}>
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Date Range</h3>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
                      {openSections.dateRange ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                    </Button>
                  </CollapsibleTrigger>
                </div>
                <CollapsibleContent className="pt-2">
                  <RadioGroup value={filters.dateRange} onValueChange={handleDateRangeChange} className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="all" id="date-all" />
                      <Label htmlFor="date-all">All Time</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="today" id="date-today" />
                      <Label htmlFor="date-today">Today</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="week" id="date-week" />
                      <Label htmlFor="date-week">This Week</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="month" id="date-month" />
                      <Label htmlFor="date-month">This Month</Label>
                    </div>
                  </RadioGroup>
                </CollapsibleContent>
              </Collapsible>

              {type === "knowledge" && (
                <>
                  <Separator />

                  <Collapsible open={openSections.sortBy} onOpenChange={() => toggleSection("sortBy")}>
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">Sort By</h3>
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
                          {openSections.sortBy ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                        </Button>
                      </CollapsibleTrigger>
                    </div>
                    <CollapsibleContent className="pt-2">
                      <RadioGroup value={filters.sortBy || null} onValueChange={handleSortByChange} className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="relevance" id="sort-relevance" />
                          <Label htmlFor="sort-relevance">Relevance</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="newest" id="sort-newest" />
                          <Label htmlFor="sort-newest">Newest First</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="oldest" id="sort-oldest" />
                          <Label htmlFor="sort-oldest">Oldest First</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="most-viewed" id="sort-most-viewed" />
                          <Label htmlFor="sort-most-viewed">Most Viewed</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="most-liked" id="sort-most-liked" />
                          <Label htmlFor="sort-most-liked">Most Helpful</Label>
                        </div>
                      </RadioGroup>
                    </CollapsibleContent>
                  </Collapsible>
                </>
              )}

              <Button variant="outline" size="sm" onClick={resetFilters} className="w-full">
                Reset Filters
              </Button>
            </div>
          </PopoverContent>
        </Popover>

        {activeFilters.length > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9 px-2 text-xs">
            Clear all
          </Button>
        )}
      </div>

      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-1">
          {activeFilters.map((filter) => (
            <Badge key={filter} variant="outline" className="flex items-center gap-1 px-2 py-1">
              {filter}
              <Button variant="ghost" size="icon" className="h-4 w-4 p-0 ml-1" onClick={() => removeFilter(filter)}>
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}

