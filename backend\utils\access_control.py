from typing import Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status, Depends
from backend.models import User, Project, ProjectAccess, AccessLevelEnum
from backend.database import get_db
from backend.dependencies import get_current_user

def check_project_access(
    db: Session,
    user: User,
    project_id: int,
    required_level: AccessLevelEnum = AccessLevelEnum.READ
) -> bool:
    """Check if user has required access level to project"""
    # Admins always have access
    if hasattr(user, 'admin') and user.admin is not None:
        return True

    access = db.query(ProjectAccess).filter(
        ProjectAccess.user_id == user.user_id,
        ProjectAccess.project_id == project_id
    ).first()

    if not access:
        return False

    access_levels = {
        AccessLevelEnum.READ: 1,
        AccessLevelEnum.WRITE: 2,
        AccessLevelEnum.ADMIN: 3
    }

    return access_levels[access.access_level] >= access_levels[required_level]

def require_project_access(required_level: AccessLevelEnum = AccessLevelEnum.READ):
    """Dependency for checking project access"""
    def check_access(
        project_id: int,
        db: Session = Depends(get_db),
        current_user: User = Depends(get_current_user)
    ):
        if not check_project_access(db, current_user, project_id, required_level):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient project access"
            )
        return current_user
    return check_access