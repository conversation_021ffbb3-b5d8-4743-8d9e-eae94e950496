from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import or_
from typing import Optional
from ..database import get_db
from ..models import Issue, KnowledgeBase
router = APIRouter()

@router.get("/projects/{project_id}/issues")
async def search_issues(
    project_id: int,
    query: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Search issues by query string.
    If query is None or empty, returns all issues for the project.

    Args:
        project_id (int): ID of the project to search issues for
        query (Optional[str]): Search query string to filter issues
        db (Session): Database session dependency

    Returns:
        dict: {
            "items": List of issue dictionaries containing issue details,
            "total": Total number of matching issues
        }

    Raises:
        HTTPException: 500 error if database operation fails
    """
    try:
        # Start with a base query for issues in this project
        base_query = db.query(Issue).filter(Issue.project_id == project_id)

        # Apply search filter if query is provided
        if query and query.strip():
            search_filter = or_(
                Issue.title.ilike(f"%{query}%"),
                Issue.error_message.ilike(f"%{query}%"),
                Issue.description.ilike(f"%{query}%"),
                Issue.error_code.ilike(f"%{query}%")
            )
            base_query = base_query.filter(search_filter)

        # Get total count for pagination
        total = base_query.count()

        # Get all issues
        issues = base_query.all()

        # Format the response
        items = []
        for issue in issues:
            items.append({
                "issue_id": issue.issue_id,
                "title": issue.title,
                "error_message": issue.error_message,
                "description": issue.description,
                "category": issue.category,
                "status": issue.status,
                "created_at": issue.created_at
            })

        return {
            "items": items,
            "total": total
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/projects/{project_id}/knowledge-base")
async def search_knowledge_base(
    project_id: int,
    query: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Search knowledge base entries by query string.
    If query is None or empty, returns all knowledge base entries for the project.

    Args:
        project_id (int): ID of the project to search knowledge base entries for
        query (Optional[str]): Search query string to filter entries
        db (Session): Database session dependency

    Returns:
        dict: {
            "items": List of knowledge base entry dictionaries containing entry details,
            "total": Total number of matching entries
        }

    Raises:
        HTTPException: 500 error if database operation fails
    """
    try:
        # Start with a base query for knowledge base entries in this project
        base_query = db.query(KnowledgeBase).filter(KnowledgeBase.project_id == project_id)

        # Apply search filter if query is provided
        if query and query.strip():
            search_filter = or_(
                KnowledgeBase.title.ilike(f"%{query}%"),
                KnowledgeBase.content.ilike(f"%{query}%")
            )
            base_query = base_query.filter(search_filter)

        # Get total count for pagination
        total = base_query.count()

        # Get all knowledge base entries
        kb_entries = base_query.all()

        # Format the response
        items = []
        for kb in kb_entries:
            items.append({
                "kb_id": kb.kb_id,
                "title": kb.title,
                "content": kb.content,
                "created_at": kb.created_at
            })

        return {
            "items": items,
            "total": total
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

