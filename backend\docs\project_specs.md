
# **ExTrack – Centralized Exception Management & Solution Repository**

## **Project Overview**

ExTrack is a web-based platform designed to help employees log, search, and resolve exceptions efficiently. It provides a centralized repository where users can report issues, find solutions, and contribute knowledge.

### **Technology Stack**

- **Frontend**: Next.js (App Router), Tailwind CSS, shadcn/ui, Lucide Icons
- **Backend**: FastAPI (Python), MySQL
- **Authentication**: JWT, Firebase, or Auth0
- **Hosting**: Yet to decide

### **User Roles**

- **Employees (Users)**: Can log exceptions, search issues, and contribute solutions
- **Admins**: Manage users, validate solutions, assign projects, and generate reports

---

## **Core Pages and Functionalities**

### **Landing Page (Public Page)**

- Welcome message with an introduction to ExTrack
- Project selection cards allowing users to choose a project
- Page transition to authentication when a project is selected

### **Authentication Flow (Login and Registration)**

- Login page using JWT, Firebase, or Auth0 for authentication
- Successful login redirects users to the home page of the selected project

### **Home Page**

- Global search bar to search across all logged issues, solutions, and knowledge base
- Minimalist interface with clear navigation
- Live issue tracking widget (optional)
- Recent issues and popular solutions section
- Persistent sidebar navigation for seamless access across pages
- Mobile-responsive design with an adaptive layout
- Dark mode support (optional)

### **Issues Page (Common for Users and Admins)**

### **Exception Logging and Categorization**

- Users can log new exceptions with:
    - Error message and code
    - Affected system/module (OMS, WMS, Automation, etc.)
    - Exception type (Database, API, UI, etc.)
    - Frequency and impact level (Low, Medium, High)
    - Attachments (logs/screenshots, optional)
- User interface includes:
    - Structured form with inline validation
    - Auto-suggest for common errors
    - Expandable issue cards displaying full details
    - Ability to edit or delete user-submitted issues before approval

### **Advanced Search and Filtering System**

- Global search bar with real-time lookup
- Filters for refined search results:
    - Keyword-based search
    - Category-based filtering (Common Issues, Exceptions, Miscellaneous)
    - Affected system/module (OMS, WMS, Automation, etc.)
    - Exception type (Database, API, UI, etc.)
    - Resolution status (Pending, Resolved, Needs Review)
- Scalable interface supporting pagination or infinite scrolling
- Dynamic filtering without page reload
- Both users and admins can submit new issues

### **Knowledge Bank Page (Common for Users and Admins)**

- Users and admins can add new knowledge base entries
- Click-to-expand system revealing full solution details
- Advanced search and filtering options
- Community contributions allowing users to:
    - Submit solutions for admin review
    - Edit or update submitted solutions before approval

### **Admin Functionalities**

### **User Management**

- View all registered users (Employees and Admins)
- Assign and change roles (Promote or demote users between Employee and Admin)
- Restrict users from submitting issues or solutions if necessary
- Deactivate or delete users (soft-delete or permanent removal)

### **Issue and Solution Validation**

- Approve or reject submitted exceptions
- Validate and approve solutions before they appear in the knowledge base

### **System Analytics and Reports**

- Track total issues logged, categorized by type, impact level, and system/module
- Monitor resolution time and average response time for issues
- Generate user activity reports, including top contributors and inactive users

### **Project and Access Management**

- Assign specific users to projects
- Restrict access to certain issues based on role or project

---

## **Additional Features and Enhancements**

- Persistent sidebar navigation across all pages for easy access
- Mobile-responsive user interface with adaptive layouts and a collapsible menu
- Dark mode support (optional)
- Optimized for scalability and performance to handle large datasets
- Version control for issues and solutions to track edits and updates

---

## **Technology Stack and Implementation Plan**

### **Frontend**

- Next.js (App Router)
- Tailwind CSS
- shadcn/ui Components
- Lucide Icons

### **Backend**

- FastAPI (Python)
- MySQL Database
- Authentication with Auth0

---

## **Summary and Key Deliverables**

### **Pages**

1. Landing Page with project selection
2. Authentication Page for login and registration
3. Home Page with global search, recent issues, and a sidebar
4. Issues Page for logging issues, filtering, and managing exceptions
5. Knowledge Bank for searching, contributing, and viewing solutions
6. Admin only page/dashboard for user management, issue validation, and reports

### **Features**

- Global search and advanced filters
- Issue logging with categorization
- Knowledge contribution and approval system
- Admin management for users, projects, and reports
- Scalable, mobile-responsive, and modern UI

---