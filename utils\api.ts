export async function fetchWithAuth(url: string, options: RequestInit = {}) {
  // For token endpoint, use form-urlencoded content type as FastAPI OAuth expects this format
  const isTokenEndpoint = url.includes('/token');
  
  const headers = {
    'Content-Type': isTokenEndpoint ? 'application/x-www-form-urlencoded' : 'application/json',
    ...options.headers,
  };

  try {
    // For token endpoint, convert JSON body to form data format
    if (isTokenEndpoint && options.body && typeof options.body === 'string') {
      try {
        const jsonBody = JSON.parse(options.body);
        const formData = new URLSearchParams();
        Object.entries(jsonBody).forEach(([key, value]) => {
          formData.append(key, String(value));
        });
        options.body = formData;
      } catch (e) {
        console.error('Error parsing JSON body:', e);
      }
    }

    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include',
    });

    if (response.status === 401) {
      const currentPath = window.location.pathname;
      const currentSearch = window.location.search;
      window.location.href = `/auth/login?returnUrl=${encodeURIComponent(currentPath + currentSearch)}`;
      return null;
    }

    return response;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}