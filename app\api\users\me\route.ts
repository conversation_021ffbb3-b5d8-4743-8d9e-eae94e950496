import { getToken } from "next-auth/jwt"
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL']|| 'http://localhost:8000';

export async function GET(request: NextRequest) {
  try {
    const token = await getToken({ req: request as any });

    if (!token) {
      return new NextResponse(
        JSON.stringify({ error: "Not authenticated" }),
        { status: 401 }
      );
    }

    if (!token['accessToken']) {
      console.error("Token exists but no accessToken found:", token);
      return new NextResponse(
        JSON.stringify({ error: "Invalid authentication token" }),
        { status: 401 }
      );
    }

    // Try to get user data from backend
    try {
      const apiUrl = `${API_BASE_URL}/users/me`;
      console.log(`Attempting to fetch from: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token['accessToken']}`,
          'Content-Type': 'application/json'
        },
        cache: 'no-store'
      }).catch(error => {
        console.error('Fetch error:', error);
        throw new Error(`Failed to connect to backend at ${apiUrl}. Is the backend server running?`);
      });

      if (!response.ok) {
        // If token is expired, try to refresh it
        if (response.status === 401) {
          try {
            // Attempt to refresh the token
            const refreshResponse = await fetch(`${API_BASE_URL}/refresh-token`, {
              method: 'POST',
              credentials: 'include',
              headers: {
                'Cookie': request.headers.get('cookie') || '',
              }
            });

            if (refreshResponse.ok) {
              // Token refreshed successfully, get the new token
              const refreshData = await refreshResponse.json();
              
              // Try the request again with the new token
              const retryResponse = await fetch(`${API_BASE_URL}/users/me`, {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${refreshData.access_token}`,
                  'Content-Type': 'application/json'
                },
                cache: 'no-store'
              });

              if (retryResponse.ok) {
                const userData = await retryResponse.json();
                
                // Return the user data along with the new token for the client to update
                return NextResponse.json({
                  ...userData,
                  new_access_token: refreshData.access_token
                });
              }
            }
          } catch (refreshError) {
            console.error("Error refreshing token:", refreshError);
          }
        }

        // If we get here, either the token refresh failed or there was another error
        const errorData = await response.json().catch(() => ({}));
        console.error(`Backend request failed with status ${response.status}:`, errorData);
        
        return new NextResponse(
          JSON.stringify({ 
            error: errorData.detail || `Backend request failed with status ${response.status}` 
          }),
          { status: response.status }
        );
      }

      const userData = await response.json();
      return NextResponse.json(userData);
    } catch (fetchError) {
      // If the backend request fails completely, return session data as fallback
      console.error("Error fetching from backend, using session data as fallback:", fetchError);
      
      // Make sure to check both possible locations for admin status
      const isAdmin = token.is_admin === true || token.role === 'admin';
      
      console.log('API users/me fallback - admin status:', { 
        isAdmin, 
        tokenIsAdmin: token.is_admin, 
        tokenRole: token.role 
      });
      
      return NextResponse.json({
        id: token.sub,
        name: token.name,
        email: token.email,
        is_admin: isAdmin,
        user_id: token.sub,
        _source: "session_fallback"
      });
    }
  } catch (error) {
    console.error("Error in /api/users/me:", error);
    return new NextResponse(
      JSON.stringify({ error: error instanceof Error ? error.message : "Internal server error" }),
      { status: 500 }
    );
  }
}
